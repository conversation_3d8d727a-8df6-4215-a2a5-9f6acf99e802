import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type {
  CancelTodoParams,
  CompleteTodoParams,
  CreateTodoParams,
  TodoEntity,
  UpdateTodoParams,
} from './types';

export const queryTodoList = (
  params: PageRequestParamsType & { loginPerson?: string },
): Promise<PageResponseDataType<TodoEntity>> => {
  return request('/ipmspassport/store/todoTask/pageQuery', { data: params });
};

export const createTodo = (params: CreateTodoParams) => {
  return request('/ipmspassport/store/todoTask/create', { data: params });
};

export const updateTodo = (params: UpdateTodoParams) => {
  return request('/ipmspassport/store/todoTask/update', { data: params });
};

export const cancelTodo = (params: CancelTodoParams) => {
  return request('/ipmspassport/store/todoTask/cancel', { data: params });
};

export const completeTodo = (params: CompleteTodoParams) => {
  return request('/ipmspassport/store/todoTask/complete', { data: params });
};
