import LeftTitle from '@/components/LeftTitle';
import { compressImage } from '@/utils/fileUtils';
import {
  DrawerForm,
  ProCard,
  ProFormDatePicker,
  ProFormDependency,
  ProFormField,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { type GetProps, Image } from 'antd';
import { useForm } from 'antd/es/form/Form';
import BraftEditor, { type EditorState } from 'braft-editor';
import 'braft-editor/dist/index.css';
import { useState } from 'react';
import { queryPushDetail } from '../services';
import { type CreateDrawerProps } from '../types/CreateDrawerProps';
import { type MessageEntity } from '../types/MessageEntity';
import { NoticeType, NoticeTypeOptions } from '../types/noticeType';
import RichEditor from '@/components/RichEditor';
const rules = [{ required: true }];
export default ({
  title,
  visible = true,
  onCancel,
  recordId,
  onOk,
  readOnly,
}: CreateDrawerProps<number, MessageEntity>) => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });
  const [content, setContent] = useState<EditorState>('');
  const [form] = useForm();
  const [previewImage, setPreviewImage] = useState('');
  useAsyncEffect(async () => {
    if (visible && recordId) {
      const result = await queryPushDetail({ id: recordId });
      const { content: richContent, ...values } = result;
      setContent(BraftEditor.createEditorState(richContent));

      form.setFieldsValue({
        ...values,
        image: values.image
          ? [
              {
                uid: '-1',
                name: 'image.png',
                status: 'done',
                url: values.image,
              },
            ]
          : [],
      });
    } else {
      form.resetFields();
      form.setFieldValue('content', '');
      setContent('');
    }
  }, [visible]);

  const editorProps: GetProps<typeof BraftEditor> = {};
  const submitterProps: GetProps<typeof DrawerForm> = {};
  if (readOnly) {
    editorProps.controls = [];
    editorProps.extendControls = [];
    editorProps.readOnly = readOnly;
    submitterProps.submitter = false;
  }
  return (
    <DrawerForm
      width="80%"
      form={form}
      layout="vertical"
      title={title}
      open={visible}
      grid
      drawerProps={{
        destroyOnClose: true,
        onClose: onCancel,
        styles: { body: { backgroundColor: '#F2F2F2', margin: 0 } },
        maskClosable: false,
      }}
      autoFocusFirstInput
      submitTimeout={2000}
      submitter={{ submitButtonProps: { style: { display: readOnly ? 'none' : 'block' } } }}
      onFinish={onOk}
    >
      <ProFormText name="id" hidden />
      <ProCard
        bordered={false}
        title={<LeftTitle title={t('system.messageMgr.create.group.baseInfo')} />}
        className="rounded-lg"
      >
        <ProFormGroup>
          <ProFormText
            name="title"
            disabled={readOnly}
            rules={[
              ...rules,
              {
                max: 20,
              },
            ]}
            colProps={{ span: 12 }}
            label={t('system.messageMgr.column.title')}
          />
          <ProFormDatePicker
            disabled={readOnly}
            width={'100%'}
            fieldProps={{
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
            }}
            name="sendTime"
            colProps={{ span: 12 }}
            label={t('system.messageMgr.column.sendTime')}
            placeholder={t('system.messageMgr.placeholder.sendTime')}
          />
        </ProFormGroup>
        <ProFormGroup>
          <ProFormDatePicker
            width={'100%'}
            disabled={readOnly}
            fieldProps={{ showTime: true, format: 'YYYY-MM-DD HH:mm:ss' }}
            name="overdueTime"
            colProps={{ span: 12 }}
            label={t('system.messageMgr.column.overdueTime')}
            placeholder={t('system.messageMgr.placeholder.overdueTime')}
          />
          <ProFormSelect
            options={NoticeTypeOptions(intl)}
            disabled={readOnly}
            colProps={{ span: 12 }}
            rules={rules}
            name="noticeType"
            label={t('system.messageMgr.column.noticeType')}
          />
          <ProFormDependency name={['noticeType']}>
            {({ noticeType }, form) => {
              if (noticeType == NoticeType.BANNER) {
                return (
                  <>
                    <ProFormUploadButton
                      disabled={readOnly}
                      name="image"
                      label={t('system.messageMgr.create.bannerImage')}
                      action="/apigateway/public/upload/object/batch"
                      max={1}
                      fieldProps={{
                        listType: 'picture-card',
                        beforeUpload: (file) => compressImage(file),
                        onPreview: (file) => {
                          const url =
                            file.url ||
                            (file.originFileObj && URL.createObjectURL(file.originFileObj));
                          if (url) {
                            setPreviewImage(url);
                          }
                        },
                      }}
                      rules={[{ required: noticeType == NoticeType.BANNER }]}
                    />
                    <Image
                      style={{ display: 'none' }}
                      preview={{
                        visible: Boolean(previewImage),
                        src: previewImage,
                        onVisibleChange: (val) => {
                          setPreviewImage(val ? previewImage : '');
                        },
                      }}
                    />
                  </>
                );
              }
            }}
          </ProFormDependency>
        </ProFormGroup>
        <div className="richText">
          <ProFormField
            name="content"
            readonly={readOnly}
            rules={rules}
            label={t('system.messageMgr.column.content')}
            transform={(value: EditorState) => {
              return { content: value ? value.toHTML() : '' };
            }}
          >
            <RichEditor
              form={form}
              name="content"
              readOnly={readOnly}
              content={content}
              setContent={setContent}
            />
          </ProFormField>
        </div>
      </ProCard>
    </DrawerForm>
  );
};
