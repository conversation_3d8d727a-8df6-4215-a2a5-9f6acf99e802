import { queryEtcAfterDetailPagePost } from '@/pages/purchase/detail/services';
import { createReturnOrderByETC, queryMsgDetail, setRead } from '@/pages/system/message/services';
import type { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import { MsgJumpType } from '@/pages/system/message/types/MsgJumpType';
import { MsgStatus } from '@/pages/system/message/types/MsgStatus';
import { history, useIntl } from '@umijs/max';
import { Button, Drawer, Space, Spin } from 'antd';
import { useEffect, useState } from 'react';
export interface MessageDetailModalProps {
  id?: number;
  visible: boolean;
  onClose: () => void;
}

const MessageDetailModal = (props: MessageDetailModalProps) => {
  const intl = useIntl();
  const { id, visible, onClose } = props;
  const [detail, setDetail] = useState<MsgListItemEntity>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      setLoading(true);
      queryMsgDetail(id)
        .then((result) => {
          if (result) {
            setDetail(result);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id]);

  // 设置消息已读
  useEffect(() => {
    if (detail?.msgStatus === MsgStatus.NoRead) {
      setRead([detail.id!]);
    }
  }, [detail]);

  return (
    <Drawer
      title={intl.formatMessage({ id: 'system.message.list.messageDetail' })}
      open={visible}
      onClose={onClose}
      maskClosable={false}
      footer={false}
      width={1080}
    >
      <Spin spinning={loading}>
        <div className="text-lg font-semibold">{detail?.title}</div>
        <div className="text-gray-500 mt-1">{detail?.createTime}</div>
        {detail?.bizType == 1 && <div className="text-gray-700 my-4">{detail?.content}</div>}
        {/* 展示富文本 */}
        {detail?.bizType == 0 && (
          <div
            className="my-4 markdown-body"
            dangerouslySetInnerHTML={{ __html: detail?.content ?? '' }}
          />
        )}
        <Space>
          {MsgJumpType.ETC_PURCHASE_ORDER === detail?.jumpType && (
            <Button
              onClick={() => {
                history.push(`/purchase/detail?${detail?.jumpParam}`);
                onClose();
              }}
            >
              {intl.formatMessage({ id: 'system.message.list.viewPurchaseOrder' })}
            </Button>
          )}
          {MsgJumpType.REORDER_RECOMMENDATION === detail?.jumpType && (
            <Button
              onClick={() => {
                history.push(`/purchase/stockUp/detail?${detail?.jumpParam}`);
                onClose();
              }}
            >
              {intl.formatMessage({ id: 'system.message.list.viewReorderRecommendation' })}
            </Button>
          )}
          {MsgJumpType.ETC_RETURN_ORDER === detail?.jumpType && (
            <Button
              onClick={() => {
                setLoading(true);
                createReturnOrderByETC(JSON.parse(detail?.jumpParam)?.afterSalesNo)
                  .then((result) => {
                    if (result) {
                      history.push(`/purchase/returns/detail?returnId=${result}`);
                      onClose();
                    }
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              {intl.formatMessage({ id: 'system.message.list.createReturnOrder' })}
            </Button>
          )}
          {[MsgJumpType.ETC_RETURN_ORDER, MsgJumpType.ETC_RETURN_LOGISTICS].includes(
            detail?.jumpType!,
          ) && (
            <Button
              onClick={() => {
                setLoading(true);
                queryEtcAfterDetailPagePost({
                  afterSalesNo: JSON.parse(detail?.jumpParam)?.afterSalesNo,
                })
                  .then((result) => {
                    if (result) {
                      window.open(result);
                    }
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              {intl.formatMessage({ id: 'system.message.list.viewDetail' })}
            </Button>
          )}
        </Space>
      </Spin>
    </Drawer>
  );
};

export default MessageDetailModal;
