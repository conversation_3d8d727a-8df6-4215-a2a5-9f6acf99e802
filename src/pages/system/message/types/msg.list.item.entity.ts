import { MsgBizType } from '@/pages/system/message/types/MsgBizType';
import { MsgChannel } from '@/pages/system/message/types/MsgChannel';
import { MsgJumpType } from '@/pages/system/message/types/MsgJumpType';
import { MsgStatus } from '@/pages/system/message/types/MsgStatus';
import { NoticeType } from '@/pages/system/messageMgr/types/noticeType';

export interface MsgListItemEntity {
  /**
   * 账户ID
   */
  accountId?: string;
  /**
   * 业务类型：0-系统通知1-一体系通知
   */
  bizType?: MsgBizType;
  /**
   * 内容
   */
  content?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 主键ID
   */
  id?: number;
  /**
   * 是否删除0-未删除1-已删除
   */
  isDelete?: 0 | 1;
  /**
   * 跳转参数
   */
  jumpParam?: any;
  /**
   * 跳转类型:0-一体系订单,1-一体系退货单,2-补货建议
   */
  jumpType?: MsgJumpType;
  /**
   * None
   */
  memberId?: string;
  /**
   * 消息状态0-未读1-已读
   */
  msgStatus?: MsgStatus;
  /**
   * 通知渠道:0-PC1-小程序
   */
  noticeChannel?: MsgChannel;
  /**
   * 通知方式:0-站内信1-通知2-弹窗
   */
  noticeType?: NoticeType;
  /**
   * 过期时间
   */
  overdueTime?: string;
  /**
   * 发送时间
   */
  sendTime?: string;
  /**
   * 标题
   */
  title?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;

  image: string;
}
