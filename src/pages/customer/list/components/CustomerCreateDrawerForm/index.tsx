import FunProFormUploadButton from '@/components/FunProFormUploadButton';
import LeftTitle from '@/components/LeftTitle';
import { cstTypeOptions } from '@/pages/customer/list/types/cst.type';
import { queryAccountSelectByStoreId, queryStoreByAccount } from '@/pages/personnel/user/services';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT } from '@/utils/Constants';
import {
  REG_EMAIL_RULE,
  REG_LENGTH_REMARK_RULE,
  REG_LENGTH_RULE,
  REG_ONLY_ALPHA_AND_DIGIT_RULE,
  REQUIRED_RULES,
} from '@/utils/RuleUtils';
import type { EditableFormInstance } from '@ant-design/pro-components';
import { DrawerForm, EditableProTable, ProFormGroup } from '@ant-design/pro-components';
import { ProFormDependency, ProFormMoney, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import type { FormInstance } from 'antd';
import { ConfigProvider, Flex, Form, Radio, Switch } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { find, isEmpty, uniq, uniqueId } from 'lodash';
import { useRef, useState } from 'react';
import CustomerContactTableColumns from '../../config/CustomerContactTableColumns';
import { CustomerStatusOptions, DeliveryAmountTypeOptions } from '../../config/customerOptions';
import { getCstDetail, getTagList } from '../../services';
import type { CustomerContactEntity } from '../../types/CustomerContactEntity';
import { type CustomerCreateDrawerFormType } from '../../types/CustomerCreateDrawerFormType';
import {
  CustomerStatus,
  DeliveryAmountType,
  type CustomerSaveEntity,
  type Settle,
} from '../../types/CustomerSaveEntity';
import AddressInfo from './AddressInfo';
import SettlementInfo from './SettlementInfo';
const labelCol = { span: 12 };
const colProps = { span: 8 };
/**
 * 基础信息
 * @param props CommonModelForm
 * @returns
 */
const renderBaseInfoFormItems = (
  form: FormInstance,
  recordId: string,
  intl: ReturnType<typeof useIntl>,
) => {
  // 接收 intl
  return (
    <ProFormGroup
      style={{
        backgroundColor: 'white',
        padding: 24,
        marginTop: 16,
        borderRadius: 8,
      }}
      title={
        <LeftTitle
          title={intl.formatMessage({ id: 'customer.customerList.createForm.group.baseInfo' })}
        />
      }
    >
      <ProFormText name={['base', 'id']} hidden />
      <ProFormText
        rules={[REQUIRED_RULES, REG_LENGTH_RULE]}
        name={['base', 'cstName']}
        labelCol={labelCol}
        colProps={colProps}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerName' })}
      />
      <ProFormText
        name={['base', 'cstSn']}
        labelCol={labelCol}
        colProps={colProps}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerSn' })}
        rules={[REG_ONLY_ALPHA_AND_DIGIT_RULE, { required: !isEmpty(recordId) }]}
      />
      <ProFormText
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'nickName']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.nickName' })}
        rules={[REG_LENGTH_REMARK_RULE]}
      />
      <ProFormText
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'abn']}
        label="ABN"
        rules={[REG_LENGTH_REMARK_RULE, REQUIRED_RULES]}
      />
      <ProFormSelect
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'cstStatus']}
        label={intl.formatMessage({ id: 'customer.customerList.table.column.status' })}
        options={CustomerStatusOptions}
      />
      <ProFormSelect
        rules={[REQUIRED_RULES]}
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'storeId']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.store' })}
        onChange={() => {
          form.setFieldValue(['base', 'salesmanId'], undefined);
        }}
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={() => queryStoreByAccount({ status: 1 })}
      />

      <ProFormSelect
        name={['base', 'salesmanId']}
        labelCol={labelCol}
        colProps={colProps}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.salesman' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        dependencies={['base', 'storeId']}
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={async (params) => {
          const {
            base: { storeId },
          } = params;
          if (isEmpty(storeId)) return [];
          return queryAccountSelectByStoreId({ storeId });
        }}
        rules={[REQUIRED_RULES]}
      />
      <ProFormSelect
        rules={[REQUIRED_RULES]}
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'cstType']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerType' })}
        options={cstTypeOptions}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerTags' })}
        name="tagIdList"
        labelCol={labelCol}
        colProps={colProps}
        fieldProps={{
          mode: 'multiple',
          maxTagCount: 3,
          allowClear: true,
          fieldNames: { label: 'tagName', value: 'id' },
        }}
        request={() => getTagList({ tagStatus: 0, tagType: 1 })}
      />

      <ProFormText
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'universalEmail']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.universalEmail' })}
        rules={[REG_EMAIL_RULE]}
      />

      <ProFormText
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'financeEmail']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.financeEmail' })}
        rules={[REG_EMAIL_RULE]}
      />

      <ProFormGroup colProps={{ span: 8 }}>
        <Form.Item
          label={' '}
          name={['base', 'sendFinanceEmailFlag']}
        >
          <div className='flex'>
            <span className='mx-1'>
              {
                intl.formatMessage({
                  id: 'customer.customerList.createForm.label.sendFinanceEmailFlag',
                })
              }
            </span>
            <Switch defaultValue={true} />
          </div>
        </Form.Item>
      </ProFormGroup>

      <ProFormSelect
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.deliverWay' })}
        name={['base', 'deliveryAmountType']}
        labelCol={labelCol}
        colProps={colProps}
        options={DeliveryAmountTypeOptions}
      />
      <ProFormDependency name={['base']}>
        {(props) => {
          console.log(props);
          const deliverWay = props.base.deliveryAmountType;
          if (deliverWay == DeliveryAmountType.Fixed) {
            return (
              <ProFormMoney
                label={intl.formatMessage({
                  id: 'customer.customerList.createForm.label.deliveryAmount',
                })}
                name={['base', 'deliveryAmount']}
                labelCol={labelCol}
                colProps={colProps}
                placeholder={intl.formatMessage({
                  id: 'customer.customerList.createForm.placeholder.deliveryAmount',
                })}
                rules={[REQUIRED_RULES]}
                fieldProps={{
                  precision: 2,
                  max: MAX_AMOUNT,
                  min: 0,
                }}
                locale="en-US"
              />
            );
          }
          return null;
        }}
      </ProFormDependency>

      <ProFormText
        labelCol={{ span: 2 }}
        colProps={{ span: 24 }}
        name={['base', 'remark']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.remark' })}
        rules={[REG_LENGTH_REMARK_RULE]}
      />
      <FunProFormUploadButton
        name="images"
        labelCol={labelCol}
        colProps={colProps}
        max={1}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerPhoto' })}
      />
    </ProFormGroup>
  );
};

/**
 * 联系人信息
 */
const RenderContact = () => {
  const intl = useIntl();
  const editorFormRef = useRef<EditableFormInstance<CustomerContactEntity>>();
  return (
    <ProFormGroup
      title={
        <LeftTitle
          title={intl.formatMessage({ id: 'customer.customerList.createForm.group.contactInfo' })}
        />
      }
      style={{
        backgroundColor: 'white',
        padding: 24,
        marginTop: 16,
        borderRadius: 8,
      }}
    >
      <ConfigProvider
        theme={{
          components: {
            InputNumber: {
              controlWidth: 80,
            },
          },
        }}
      >
        <EditableProTable<CustomerContactEntity>
          className="mt-4"
          search={false}
          name="contacts"
          rowKey="id"
          editableFormRef={editorFormRef}
          scroll={{ x: 'max-content' }}
          recordCreatorProps={{
            record: (index) => ({ id: uniqueId('contact_'), isDefault: index == 0 ? 1 : 0 }),
            creatorButtonText: intl.formatMessage({
              id: 'customer.customerList.createForm.button.addContact',
            }),
          }}
          pagination={false}
          editable={{
            type: 'multiple',
            actionRender: (_, __, defaultDom) => [defaultDom.delete],
          }}
          columns={[
            {
              title: intl.formatMessage({ id: 'common.column.index' }),
              valueType: 'index',
              editable: false,
              width: 40,
              fixed: 'left',
            },
            {
              title: intl.formatMessage({
                id: 'customer.customerList.createForm.contactTable.column.isDefaultContact',
              }),
              align: 'center',
              width: 100,
              dataIndex: 'isDefault',
              fixed: 'left',
              render: (_, record) => {
                const rows = editorFormRef.current?.getRowsData?.();
                return (
                  <Radio
                    checked={record.isDefault === 1}
                    onChange={() => {
                      rows?.forEach((t) => {
                        const { id } = t;
                        editorFormRef.current?.setRowData?.(id, {
                          isDefault: id == record?.id ? 1 : 0,
                        });
                      });
                    }}
                  />
                );
              },
              renderFormItem: (_, { record }) => {
                const rows = editorFormRef.current?.getRowsData?.();
                if (rows) {
                  const checkRow = find(rows, (t) => t.isDefault === 1);
                  if (isEmpty(checkRow)) {
                    editorFormRef.current?.setRowData?.(0, {
                      isDefault: 1,
                    });
                  }
                }
                return (
                  <Radio
                    checked={record?.isDefault === 1}
                    onChange={() => {
                      rows?.forEach((t) => {
                        const { id } = t;
                        editorFormRef.current?.setRowData?.(id, {
                          isDefault: id == record?.id ? 1 : 0,
                        });
                      });
                    }}
                  />
                );
              },
            },
            {
              title: (
                <Flex align="center">
                  <span className="text-[#FF7621]">*</span>
                  <span>
                    {intl.formatMessage({
                      id: 'customer.customerList.createForm.contactTable.column.contactName',
                    })}
                  </span>
                </Flex>
              ),
              dataIndex: 'firstName',
              width: 100,
              colSpan: 2,
              formItemProps: {
                rules: [requiredProps],
              },
            },
            {
              title: (
                <Flex align="center">
                  <span className="text-[#FF7621]">*</span>
                  <span>
                    {intl.formatMessage({
                      id: 'customer.customerList.createForm.contactTable.column.contactName',
                    })}
                  </span>
                </Flex>
              ),
              dataIndex: 'lastName',
              width: 100,
              colSpan: 0,
              formItemProps: {
                rules: [requiredProps],
              },
            },
            ...CustomerContactTableColumns(intl),
            {
              title: intl.formatMessage({ id: 'common.column.operation' }),
              valueType: 'option',
              align: 'center',
              width: 100,
              fixed: 'right',
              render: (text, record, _, action) => (
                <a
                  key="address_editable"
                  onClick={() => {
                    action?.startEditable?.(record.id);
                  }}
                >
                  {intl.formatMessage({ id: 'common.button.edit' })}
                </a>
              ),
            },
          ]}
        />
      </ConfigProvider>
    </ProFormGroup>
  );
};

export default (props: CustomerCreateDrawerFormType) => {
  const intl = useIntl();
  const [form] = useForm();
  const [settleInfo, setSettleInfo] = useState<Settle>();
  const { get } = useModel('dictModel');
  useAsyncEffect(async () => {
    if (isEmpty(props.recordId)) {
      form.resetFields();
      setSettleInfo(undefined);
    } else {
      const data = await getCstDetail({ cstId: props.recordId });
      if (!isEmpty(data)) {
        const { addresses, images, tags, settle, billings, contacts } = data;
        setSettleInfo(settle);
        if (isEmpty(settle)) {
          delete data.settle;
        } else {
          data.settle = {
            ...settle,
            isMultiCurrency: settle.isMultiCurrency ? 1 : 0,
            gstExcluded: settle.gstExcluded ? 1 : 0,
            creditTermsType: settle.settleType,
          };
        }
        if (isEmpty(contacts)) {
          delete data.contacts;
        } else {
          data.contacts = contacts?.map((t) => ({
            ...t,
            positions: t.position.split(','),
          }));
        }
        if (!isEmpty(addresses)) {
          data.addresses = addresses?.map((t) => ({
            ...t,
            addressCode: [t.provinceCode, t.prefectureCode],
          }));
        } else {
          delete data.addresses;
        }
        if (!isEmpty(images)) {
          data.images = images?.map((t) => ({ uid: t.id, url: t.url }));
        } else {
          delete data.images;
        }
        if (!isEmpty(tags)) {
          data.tagIdList = uniq(tags?.map((t) => t.tagId));
        }
        delete data.tags;
        console.log(data);

        form.setFieldsValue(data);
      }
    }
  }, [props.visible]);

  return (
    <DrawerForm<CustomerSaveEntity>
      form={form}
      grid
      validateTrigger="onBlur"
      title={props.title}
      open={props.visible}
      width={1300}
      drawerProps={{
        maskClosable: false,
        styles: { body: { backgroundColor: '#F2F2F2', paddingTop: 10 } },
        onClose: props.onCancel,
      }}
      initialValues={{
        base: {
          cstStatus: CustomerStatus.ENABLE,
          sendFinanceEmailFlag: true
        },
      }}
      onFinish={async (values) => {
        const { addresses, billings, tagIdList, contacts, settle, images } = values;
        if (!isEmpty(addresses)) {
          const newAddress = addresses?.map((t) => {
            const { addressCode } = t;
            if (t?.id?.startsWith('address_')) {
              delete t.id;
            }
            if (addressCode) {
              const [provinceCode, prefectureCode] = addressCode;
              delete t.addressCode;
              t.provinceCode = provinceCode;
              t.prefectureCode = prefectureCode;
              t.provinceName = get(provinceCode);
              t.prefectureName = get(prefectureCode);
            }
            return t;
          });
          values.addresses = newAddress;
        } else {
          delete values.addresses;
        }
        if (!isEmpty(tagIdList)) {
          values.tags = tagIdList?.map((t) => ({ tagId: t }));
        }
        delete values.tagIdList;
        if (isEmpty(billings?.[0])) {
          delete values.billings;
        }
        if (!isEmpty(contacts)) {
          values.contacts = contacts?.map((t) => {
            const { id } = t;
            if (id?.startsWith('contact_')) {
              delete t.id;
            }
            return t;
          });
        }
        if (!isEmpty(settle)) {
          values.settle = {
            ...settle,
            isMultiCurrency: settle.isMultiCurrency ? 1 : 0,
            gstExcluded: settle.gstExcluded ? 1 : 0,
          };
        }
        if (!isEmpty(images)) {
          console.log('images', images);
          values.images = images?.map((t) => ({ url: t.url, cstId: props.recordId }));
        }
        return props?.onOk?.(values);
      }}
      scrollToFirstError
    >
      {/* 基础信息 */}
      {renderBaseInfoFormItems(form, props.recordId, intl)}
      {/* 联系人信息 */}
      {RenderContact()}
      {/* 地址信息信息 */}
      <AddressInfo />
      {/* 结算信息 */}
      <SettlementInfo />
    </DrawerForm>
  );
};
