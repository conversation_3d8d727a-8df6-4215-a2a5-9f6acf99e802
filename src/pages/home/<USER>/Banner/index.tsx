import { useDotButton } from '@/pages/shop/topic/decoration/CraftEditor/components/ImageAds/useDotButton';
import { ViewDetailModalProps } from '@/pages/system/message';
import MessageDetailModal from '@/pages/system/message/components/MessageDetailModal';
import { queryMsgList } from '@/pages/system/message/services';
import { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import { useIntl } from '@umijs/max';
import AutoPlay from 'embla-carousel-autoplay';
import useEmblaCarousel from 'embla-carousel-react';
import { useCallback, useEffect, useState } from 'react';
import { NoticeType } from '@/pages/system/messageMgr/types/noticeType';

const Banner = () => {
  const intl = useIntl();
  const [bannerList, setBannerList] = useState<MsgListItemEntity[]>([]);
  const [viewDetailModalProps, setViewDetailModalProps] = useState<ViewDetailModalProps>({
    visible: false,
  });
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true }, [
    AutoPlay({
      // delay: 3000
    }),
  ]);

  useEffect(() => {
    queryMsgList({ pageNo: 1, pageSize: 5, isDelete: 0, noticeType: NoticeType.BANNER }).then(
      (result) => {
        setBannerList(result?.data || []);
      },
    );
  }, []);

  const handleBannerClick = (banner: MsgListItemEntity) => {
    setViewDetailModalProps({
      visible: true,
      id: banner.id,
    });
  };

  const scrollPrev = useCallback(() => emblaApi && emblaApi.scrollPrev(), [emblaApi]);
  const scrollNext = useCallback(() => emblaApi && emblaApi.scrollNext(), [emblaApi]);

  const { selectedIndex, scrollSnaps, onDotButtonClick } = useDotButton(emblaApi);
  return (
    <div className="relative overflow-hidden" ref={emblaRef}>
      <div className="flex">
        {bannerList.map((banner, index) => (
          <div
            className="flex-[0_0_100%] min-w-0 relative cursor-pointer"
            key={banner.id || index}
            onClick={() => handleBannerClick(banner)}
          >
            <div
              className="h-[445px] flex items-center justify-center text-white text-center bg-cover bg-center"
              style={{
                backgroundImage: `url(${banner.image || '/default-banner.jpg'})`,
                // backgroundColor: banner.image ? 'transparent' : '#364d79'
              }}
            >
              {!banner.image && <div className="text-xl font-bold">{banner.title}</div>}
            </div>
          </div>
        ))}
      </div>
      {bannerList.length > 1 && (
        <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {scrollSnaps.map((_, index) => (
            <span
              key={index}
              onClick={(e) => {
                e.stopPropagation();
                onDotButtonClick(index);
              }}
              className={`w-2 h-2 rounded-full cursor-pointer ${
                selectedIndex === index ? 'bg-[#F49C1F]' : 'bg-gray-300'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Navigation Arrows */}
      {/* {bannerList.length > 1 && (
        <>
          <button
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-30 text-white p-2 rounded-full hover:bg-opacity-50 transition-all"
            onClick={scrollPrev}
          >
            &lt;
          </button>
          <button
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-30 text-white p-2 rounded-full hover:bg-opacity-50 transition-all"
            onClick={scrollNext}
          >
            &gt;
          </button>
        </>
      )} */}

      <MessageDetailModal
        {...viewDetailModalProps}
        onClose={() => setViewDetailModalProps({ id: undefined, visible: false })}
      />
    </div>
  );
};

export default Banner;
