import FunProTable from '@/components/common/FunProTable';
import NumberFormatText from '@/components/common/NumberFormatText';
import SubTitle from '@/components/common/SubTitle';
import { getCstList } from '@/pages/customer/list/services';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { miniMpAntdTheme } from '@/pages/report/config/miniMpAntdTheme';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/personnel/user/services';
import isFramed from '@/utils/isFramed';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { useAccess } from '@@/exports';
import { DualAxes, type DualAxesConfig } from '@ant-design/charts';
import {
  ActionType,
  PageContainer,
  PageContainerProps,
  ProCard,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, ConfigProvider, Flex, Radio, Space } from 'antd';
import dayjs from 'dayjs';
import { defaultTo, forEach, forIn, isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import PieCharts from '../../../components/pieCharts';
import PageConfig from '../config/PageConfig';
import { PostListForGoodsTableColumns } from './config/postListForGoodsTableColumns';
import { PostListForStaffTableColumns } from './config/postListForStaffTableColumns';
import { PostListForCustomTableColumns } from './config/postListforCustomTableColumns';
import {
  getSalesGroupByBrand,
  getSalesGroupByCategory,
  getSalesOverview,
  querySalesReportGroupPage,
  querySalesTrendList,
} from './services';
import type { PieType, TrendType } from './types/ChartsType';
import { GroupType } from './types/GroupType';
import type { QueryPostListRequest } from './types/query.post.list.request.entity';
import type { QueryPostListEntity } from './types/query.post.list.response.entity';
const getTitleMap = (intl: any): Record<string, string> => ({
  saleAmount: intl.formatMessage({ id: 'report.sales.overview.saleAmount' }),
  costAmount: intl.formatMessage({ id: 'report.sales.overview.costAmount' }),
  saleGrossProfit: intl.formatMessage({ id: 'report.sales.overview.saleGrossProfit' }),
  grossProfitRate: intl.formatMessage({ id: 'report.sales.overview.grossProfitRate' }),
  saleOrderNum: intl.formatMessage({ id: 'report.sales.overview.saleOrderNum' }),
  saleCustomerNum: intl.formatMessage({ id: 'report.sales.overview.saleCustomerNum' }),
});
type ViewType = { key: string; title: string; value: string };

const SalesReport = () => {
  const intl = useIntl();
  const { initialState } = useModel('@@initialState');
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const goodsActionRef = useRef<ActionType>();
  const employeeActionRef = useRef<ActionType>();
  const { hasButtonPerms } = useAccess();
  const [viewList, setViewList] = useState<ViewType[]>([
    {
      key: 'saleAmount',
      title: intl.formatMessage({ id: 'report.sales.overview.saleAmount' }),
      value: '-',
    },
    {
      key: 'costAmount',
      title: intl.formatMessage({ id: 'report.sales.overview.costAmount' }),
      value: '-',
    },
    {
      key: 'saleGrossProfit',
      title: intl.formatMessage({ id: 'report.sales.overview.saleGrossProfit' }),
      value: '-',
    },
    {
      key: 'grossProfitRate',
      title: intl.formatMessage({ id: 'report.sales.overview.grossProfitRate' }),
      value: '-',
    },
    {
      key: 'saleOrderNum',
      title: intl.formatMessage({ id: 'report.sales.overview.saleOrderNum' }),
      value: '-',
    },
    {
      key: 'saleCustomerNum',
      title: intl.formatMessage({ id: 'report.sales.overview.saleCustomerNum' }),
      value: '-',
    },
  ]);

  const [barData, setBarData] = useState<TrendType[]>([]);
  const [saleAmountLineData, setSaleAmountLineData] = useState<TrendType[]>([]);
  const [saleCostLineData, setSaleCostLineData] = useState<TrendType[]>([]);
  const [saleGrossLineData, setSaleGrossLineData] = useState<TrendType[]>([]);
  const [saleOrderNumLineData, setSaleOrderNumLineData] = useState<TrendType[]>([]);
  const [saleCustomerNumLineData, setSaleCustomerNumLineData] = useState<TrendType[]>([]);
  const [salesGroupByBrandPieData, setSalesGroupByBrandPieData] = useState<PieType[]>([]);
  const [salesGroupByCategoryPieData, setSalesGroupByCategoryPieData] = useState<PieType[]>([]);
  const [salesGroupByBrand, setSalesGroupByBrand] = useState({
    orderBySaleAmountList: [],
    orderBySaleGrossProfitList: [],
    orderBySaleNumList: [],
  });
  const [salesGroupByCategory, setSalesGroupByCategory] = useState({
    orderBySaleAmountList: [],
    orderBySaleGrossProfitList: [],
    orderBySaleNumList: [],
  });

  const [goodsFactor, setGoodsFactor] = useState<string>('price');
  const [queryParams, setQueryParams] = useState<QueryPostListRequest>({
    startTime: dayjs().subtract(29, 'days').startOf('day').format('YYYY-MM-DD'),
    endTime: dayjs().endOf('day').format('YYYY-MM-DD'),
  });

  const querySalesOverview = async (values: QueryPostListRequest) => {
    const salesOverview = await getSalesOverview({ ...values });
    if (!isEmpty(salesOverview)) {
      //销售概览
      const viewObjList: ViewType[] = [];
      const titleMap = getTitleMap(intl);
      forIn(titleMap, (value, key) => {
        if (key === 'grossProfitRate') {
          viewObjList.push({
            key,
            title: value,
            value:
              typeof salesOverview?.[key] !== 'undefined'
                ? defaultTo((salesOverview?.[key] * 100).toFixed(2) + '%', '-')
                : '-',
          });
        } else {
          viewObjList.push({
            key,
            title: value,
            value: defaultTo(salesOverview?.[key], '-'),
          });
        }
      });
      setViewList(viewObjList);
    }
  };

  const querySalesTrendListData = async (values: QueryPostListRequest) => {
    //销售趋势
    const salesTrendList = await querySalesTrendList({ ...values });
    const saleAmountLineDataList: TrendType[] = [];
    const saleCostLineDataList: TrendType[] = [];
    const saleGrossLineDataList: TrendType[] = [];
    const barDataList: TrendType[] = [];
    const saleOrderNumLineDataList: TrendType[] = [];
    const saleCustomerNumLineDataList: TrendType[] = [];
    if (salesTrendList && salesTrendList.length > 0) {
      forEach(salesTrendList, (item) => {
        const {
          bizTime,
          grossProfitRate,
          saleAmount,
          costAmount,
          saleGrossProfit,
          saleCustomerNum,
          saleOrderNum,
        } = item;
        saleAmountLineDataList.push({
          type: intl.formatMessage({ id: 'report.sales.trends.saleAmount' }),
          value: saleAmount ?? 0,
          date: bizTime,
        });
        saleCostLineDataList.push({
          type: intl.formatMessage({ id: 'report.sales.trends.costAmount' }),
          value: costAmount ?? null,
          date: bizTime,
        });
        saleGrossLineDataList.push({
          type: intl.formatMessage({ id: 'report.sales.trends.saleGrossProfit' }),
          value: saleGrossProfit ?? null,
          date: bizTime,
        });
        barDataList.push({
          type: intl.formatMessage({ id: 'report.sales.trends.grossProfitRate' }),
          value: grossProfitRate ?? 0,
          date: bizTime,
        });
        saleOrderNumLineDataList.push({
          type: intl.formatMessage({ id: 'report.sales.trends.saleOrderNum' }),
          value: saleOrderNum ?? 0,
          date: bizTime,
        });
        saleCustomerNumLineDataList.push({
          type: intl.formatMessage({ id: 'report.sales.trends.saleCustomerNum' }),
          value: saleCustomerNum ?? 0,
          date: bizTime,
        });
      });
    }
    setSaleAmountLineData(saleAmountLineDataList);
    setSaleCostLineData(saleCostLineDataList);
    setSaleGrossLineData(saleGrossLineDataList);
    setBarData(barDataList);
    setSaleOrderNumLineData(saleOrderNumLineDataList);
    setSaleCustomerNumLineData(saleCustomerNumLineDataList);
  };

  const querySalesGroup = async (values: QueryPostListRequest) => {
    const salesGroupByBrandObj = await getSalesGroupByBrand({ ...values });
    setSalesGroupByBrand(salesGroupByBrandObj ?? {});
    const salesGroupByCategoryObj = await getSalesGroupByCategory({ ...values });
    setSalesGroupByCategory(salesGroupByCategoryObj ?? {});
    groupByGoods(goodsFactor, salesGroupByBrandObj ?? {}, salesGroupByCategoryObj ?? {});
  };

  const querySalesReport = (values: QueryPostListRequest) => {
    querySalesOverview(values);
    querySalesTrendListData(values);
    querySalesGroup(values);
  };

  useAsyncEffect(async () => {
    await querySalesReport(queryParams);
  }, []);

  //查询
  const onSearch = async (values: QueryPostListRequest) => {
    setQueryParams(values);
    await querySalesReport(values);
    actionRef.current?.reload(true);
    goodsActionRef.current?.reload(true);
    employeeActionRef.current?.reload(true);
  };

  //按商品统计
  const groupByGoods = async (type: string, salesGroupByBrandObj, salesGroupByCategoryObj) => {
    setGoodsFactor(type);
    let salesGroupByBrandPieDataList = [];
    let salesGroupByCategoryPieDataList = [];
    switch (type) {
      case 'price':
        salesGroupByBrandPieDataList = salesGroupByBrandObj.orderBySaleAmountList
          ? salesGroupByBrandObj.orderBySaleAmountList.map((item) => {
              return {
                type: item.brandName,
                value: item.brandSaleAmount,
                percent: item.brandSaleAmountRate,
              };
            })
          : [];
        setSalesGroupByBrandPieData(salesGroupByBrandPieDataList);
        salesGroupByCategoryPieDataList = salesGroupByCategoryObj.orderBySaleAmountList
          ? salesGroupByCategoryObj.orderBySaleAmountList.map((item) => {
              return {
                type: item.categoryName,
                value: item.categorySaleAmount,
                percent: item.categorySaleAmountRate,
              };
            })
          : [];
        setSalesGroupByCategoryPieData(salesGroupByCategoryPieDataList);
        break;
      case 'amount':
        salesGroupByBrandPieDataList = salesGroupByBrandObj.orderBySaleNumList
          ? salesGroupByBrandObj.orderBySaleNumList.map((item) => {
              return {
                type: item.brandName,
                value: item.brandSaleNum,
                percent: item.brandSaleNumRate,
              };
            })
          : [];
        setSalesGroupByBrandPieData(salesGroupByBrandPieDataList);
        salesGroupByCategoryPieDataList = salesGroupByCategoryObj?.orderBySaleNumList
          ? salesGroupByCategoryObj?.orderBySaleNumList.map((item) => {
              return {
                type: item.categoryName,
                value: item.categorySaleNum,
                percent: item.categorySaleNumRate,
              };
            })
          : [];
        setSalesGroupByCategoryPieData(salesGroupByCategoryPieDataList);
        break;
      case 'rate':
        salesGroupByBrandPieDataList = salesGroupByBrandObj.orderBySaleGrossProfitList
          ? salesGroupByBrandObj.orderBySaleGrossProfitList.map((item) => {
              return {
                type: item.brandName,
                value: item.brandSaleGrossProfit,
                percent: item.brandGrossProfitRate,
              };
            })
          : [];
        setSalesGroupByBrandPieData(salesGroupByBrandPieDataList);
        salesGroupByCategoryPieDataList = salesGroupByCategoryObj.orderBySaleGrossProfitList
          ? salesGroupByCategoryObj.orderBySaleGrossProfitList.map((item) => {
              return {
                type: item.categoryName,
                value: item.categorySaleGrossProfit,
                percent: item.categoryGrossProfitRate,
              };
            })
          : [];
        setSalesGroupByCategoryPieData(salesGroupByCategoryPieDataList);
        break;
    }
  };

  const config: DualAxesConfig = {
    xField: 'date',
    yField: 'value',
    legend: {
      color: {
        layout: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      },
    },
    children: [
      {
        seriesField: 'type',
        colorField: 'type',
        data: [...saleAmountLineData, ...saleCostLineData, ...saleGrossLineData],
        type: 'line',
        shapeField: 'smooth',
        axis: {
          x: {
            size: saleAmountLineData.length > 20 ? 0 : 120,
          },
          y: { position: 'left', title: '元', titlePosition: 'left' },
        },
      },
      {
        seriesField: 'type',
        colorField: 'type',
        data: barData,
        type: 'interval',
        style: { maxWidth: 40 },
        axis: {
          x: {
            size: barData.length > 20 ? 0 : 120,
          },
          y: { position: 'right', title: '%', titlePosition: 'right' },
        },
      },
    ],
  };

  const salesConfig: DualAxesConfig = {
    xField: 'date',
    yField: 'value',
    legend: {
      color: {
        layout: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      },
    },
    children: [
      {
        seriesField: 'type',
        colorField: 'type',
        data: [...saleOrderNumLineData, ...saleCustomerNumLineData],
        type: 'line',
        shapeField: 'smooth',
        axis: {
          x: {
            size: saleOrderNumLineData.length > 20 ? 0 : 120,
          },
        },
      },
    ],
  };

  let pConfig: Partial<PageContainerProps> = {};
  //@ts-ignore
  if (isFramed()) {
    pConfig = PageConfig(hasButtonPerms, intl);
  }
  return (
    <ConfigProvider
      //@ts-ignore
      theme={isFramed() ? miniMpAntdTheme : null}
    >
      <PageContainer {...pConfig} tabActiveKey={location.pathname}>
        <Flex vertical={true} wrap="wrap" gap={16}>
          <ProCard headerBordered>
            <QueryFilter onFinish={onSearch} formRef={formRef} submitter={false} className="!p-0">
              <ProFormSelect
                name="storeIdList"
                label={intl.formatMessage({ id: 'report.sales.form.store' })}
                mode={'multiple'}
                fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
                request={() => queryStoreByAccount({})}
              />
              <ProFormSelect
                name="customerIdList"
                label={intl.formatMessage({ id: 'report.sales.form.customer' })}
                mode={'multiple'}
                showSearch
                request={async (query) => {
                  const data = await getCstList({ keyword: query.keyWords, cstStatus: 0 });
                  return data?.map(({ cstId, cstName }) => ({
                    value: cstId,
                    label: cstName,
                  }));
                }}
              />
              <ProFormText
                label={intl.formatMessage({ id: 'report.sales.form.goodsInfo' })}
                name="itemFuzzy"
                placeholder={intl.formatMessage({ id: 'report.sales.form.goodsInfoPlaceholder' })}
              />
              <ProFormSelect
                label={intl.formatMessage({ id: 'report.sales.form.brand' })}
                name="brandIdList"
                showSearch={true}
                debounceTime={300}
                mode={'multiple'}
                fieldProps={{
                  filterOption: false,
                  maxTagCount: 3,
                  optionRender: (option) => <Space>{option.data.label}</Space>,
                }}
                request={(query) => {
                  const params: any = {
                    brandStatus: '1',
                    pageSize: 99,
                    pageNo: 1,
                    brandName: query.keyWords,
                  };
                  return queryGoodsPropertyPage(params, 'brand').then((result) =>
                    result.data?.map((item) => ({
                      label: item.brandName,
                      dataType: item.dataType,
                      value: item.brandId,
                    })),
                  );
                }}
              />
              <ProFormTreeSelect
                label={intl.formatMessage({ id: 'report.sales.form.category' })}
                name="categoryIdList"
                fieldProps={{
                  maxTagCount: 3,
                  treeCheckable: true,
                  filterTreeNode: (text, treeNode) => treeNode.text?.includes(text),
                }}
                request={() => {
                  const params: any = {
                    categoryStatus: 1,
                    pageSize: 999,
                    pageNo: 1,
                    isReturnTree: true,
                  };
                  return queryGoodsPropertyPage(params, 'category').then((result) =>
                    transformCategoryTree(result.data),
                  );
                }}
              />
              <ProFormSelect
                name="employeeIdList"
                label={intl.formatMessage({ id: 'report.sales.form.employee' })}
                mode={'multiple'}
                showSearch
                request={async (query) => {
                  const data = await accountListQuerySimple({ name: query.keyWords });
                  return data?.map(({ id, name }) => ({
                    value: id,
                    label: name,
                  }));
                }}
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.sales.form.startTime' })}
                initialValue={dayjs().subtract(29, 'days').startOf('day')}
                allowClear={false}
                name="startTime"
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.sales.form.endTime' })}
                initialValue={dayjs().endOf('day')}
                allowClear={false}
                name="endTime"
              />
              <Flex align="center" gap={8}>
                <Button type="primary" htmlType="submit">
                  {intl.formatMessage({ id: 'report.sales.form.query' })}
                </Button>
                <Button htmlType="reset">
                  {intl.formatMessage({ id: 'report.sales.form.reset' })}
                </Button>
              </Flex>
            </QueryFilter>
          </ProCard>
          <ProCard headerBordered>
            <Flex className="mt-6 px-6" gap={16} justify="space-between" wrap="wrap">
              {viewList &&
                viewList.map((viewItem) => (
                  <div key={viewItem.title} className="flex flex-col">
                    <span className="text-[#00000073]">
                      <span className="mr-2">{viewItem.title}</span>
                    </span>
                    <span className="text-[#000000D9] text-[24px] font-semibold mt-1 mb-2 w-[200px]">
                      {viewItem.key == 'grossProfitRate' && viewItem.value !== '-' ? (
                        viewItem.value
                      ) : (
                        <NumberFormatText
                          precision={
                            viewItem.key == 'saleOrderNum' || viewItem.key == 'saleCustomerNum'
                              ? 0
                              : 2
                          }
                          numberText={viewItem.value}
                        />
                      )}
                    </span>
                  </div>
                ))}
            </Flex>
          </ProCard>
          <ProCard
            gutter={8}
            title={<SubTitle text={intl.formatMessage({ id: 'report.sales.charts.salesTrend' })} />}
            wrap
          >
            <ProCard bordered colSpan={{ xs: 24, md: 12 }}>
              <DualAxes {...config} />
            </ProCard>
            <ProCard bordered colSpan={{ xs: 24, md: 12 }}>
              <DualAxes {...salesConfig} />
            </ProCard>
          </ProCard>
          <ProCard
            title={
              <SubTitle
                text={intl.formatMessage({ id: 'report.sales.charts.customerStatistics' })}
              />
            }
          >
            <FunProTable<QueryPostListEntity, any>
              ghost={true}
              rowKey="customerId"
              actionRef={actionRef}
              options={false}
              search={false}
              scroll={{ x: 'max-content' }}
              params={{
                ...queryParams,
                groupType: GroupType.SALES_REPORT_GROUP_BY_CST,
              }}
              requestPage={async (params, sort) => {
                if (params.startTime && params.endTime) {
                  const sortField = Object.values(sort)[0];
                  let sortType;
                  if (sortField) {
                    sortType = sortField === 'ascend' ? 'ASC' : 'DESC';
                  } else {
                    sortType = undefined;
                  }
                  return await querySalesReportGroupPage({
                    ...params,
                    sortBy: Object.keys(sort)[0] ?? undefined,
                    sortType,
                  });
                }
                return { data: [], total: 0 };
              }}
              columns={PostListForCustomTableColumns()}
            />
          </ProCard>
          <ProCard
            gutter={8}
            title={
              <SubTitle text={intl.formatMessage({ id: 'report.sales.charts.goodsStatistics' })} />
            }
            extra={
              <Radio.Group
                value={goodsFactor}
                onChange={(e) =>
                  groupByGoods(e.target.value, salesGroupByBrand, salesGroupByCategory)
                }
              >
                <Radio.Button value="price">
                  {intl.formatMessage({ id: 'report.sales.radio.saleAmount' })}
                </Radio.Button>
                <Radio.Button value="amount">
                  {intl.formatMessage({ id: 'report.sales.radio.saleNum' })}
                </Radio.Button>
                {salesGroupByBrand.orderBySaleGrossProfitList == null ||
                salesGroupByCategory.orderBySaleGrossProfitList == null ? null : (
                  <Radio.Button value="rate">
                    {intl.formatMessage({ id: 'report.sales.radio.saleGrossProfit' })}
                  </Radio.Button>
                )}
              </Radio.Group>
            }
            split="horizontal"
          >
            <ProCard gutter={8} split="vertical" wrap>
              <ProCard
                title={intl.formatMessage({ id: 'report.sales.charts.brandRatio' })}
                colSpan={{ xs: 24, md: 12 }}
              >
                <PieCharts data={salesGroupByBrandPieData} />
              </ProCard>
              <ProCard
                title={intl.formatMessage({ id: 'report.sales.charts.categoryRatio' })}
                colSpan={{ xs: 24, md: 12 }}
              >
                <PieCharts data={salesGroupByCategoryPieData} />
              </ProCard>
            </ProCard>
            <ProCard>
              <FunProTable<QueryPostListEntity, any>
                ghost={true}
                rowKey="itemSn"
                actionRef={goodsActionRef}
                options={false}
                search={false}
                scroll={{ x: 'max-content' }}
                params={{
                  ...queryParams,
                  groupType: GroupType.SALES_REPORT_GROUP_BY_ITEM,
                }}
                requestPage={async (params, sort) => {
                  if (params.startTime && params.endTime) {
                    const sortField = Object.values(sort)[0];
                    let sortType;
                    if (sortField) {
                      sortType = sortField === 'ascend' ? 'ASC' : 'DESC';
                    } else {
                      sortType = undefined;
                    }
                    return await querySalesReportGroupPage({
                      ...params,
                      sortBy: Object.keys(sort)[0] ?? undefined,
                      sortType,
                    });
                  }
                  return { data: [], total: 0 };
                }}
                columns={PostListForGoodsTableColumns()}
              />
            </ProCard>
          </ProCard>
          <ProCard
            title={
              <SubTitle text={intl.formatMessage({ id: 'report.sales.charts.staffStatistics' })} />
            }
          >
            <FunProTable<QueryPostListEntity, any>
              ghost={true}
              rowKey="employeeId"
              actionRef={employeeActionRef}
              options={false}
              search={false}
              scroll={{ x: 'max-content' }}
              params={{
                ...queryParams,
                groupType: GroupType.SALES_REPORT_GROUP_BY_EMP,
              }}
              requestPage={async (params, sort) => {
                if (params.startTime && params.endTime) {
                  const sortField = Object.values(sort)[0];
                  let sortType;
                  if (sortField) {
                    sortType = sortField === 'ascend' ? 'ASC' : 'DESC';
                  } else {
                    sortType = undefined;
                  }
                  return await querySalesReportGroupPage({
                    ...params,
                    sortBy: Object.keys(sort)[0] ?? undefined,
                    sortType,
                  });
                }
                return { data: [], total: 0 };
              }}
              columns={PostListForStaffTableColumns()}
            />
          </ProCard>
        </Flex>
      </PageContainer>
    </ConfigProvider>
  );
};

export default withKeepAlive(SalesReport);
