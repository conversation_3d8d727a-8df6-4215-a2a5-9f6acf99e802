import FunProTable from '@/components/common/FunProTable';
import NumberFormatText from '@/components/common/NumberFormatText';
import SubTitle from '@/components/common/SubTitle';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { querySupplierList } from '@/pages/purchase/supplier/services';
import { miniMpAntdTheme } from '@/pages/report/config/miniMpAntdTheme';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import isFramed from '@/utils/isFramed';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { useAccess } from '@@/exports';
import type { DualAxesConfig } from '@ant-design/charts';
import { DualAxes } from '@ant-design/charts';
import {
  ActionType,
  PageContainer,
  PageContainerProps,
  ProCard,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, ConfigProvider, Flex, Radio, Space } from 'antd';
import dayjs from 'dayjs';
import { defaultTo, forEach, forIn, isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import PieCharts from '../../../components/pieCharts';
import PageConfig from '../config/PageConfig';
import type { PieType } from '../sales/types/ChartsType';
import type { QueryPostListRequest } from '../sales/types/query.post.list.request.entity';
import { PostListForGoodsTableColumns } from './config/postListForGoodsTableColumns';
import { PostListTableColumns } from './config/postListTableColumns';
import {
  getPurchaseGroupByBrand,
  getPurchaseGroupByCategory,
  getPurchaseOverview,
  queryPurchaseReportGroupPage,
  queryPurchaseTrendList,
} from './services';
import { GroupType } from './types/GroupType';
import type { QueryPostListEntity } from './types/query.post.list.response.entity';
const getTitleMap = (intl: any): Record<string, string> => ({
  purchaseAmount: intl.formatMessage({ id: 'report.purchase.overview.purchaseAmount' }),
  purchaseOrderNum: intl.formatMessage({ id: 'report.purchase.overview.purchaseOrderNum' }),
  purchaseNum: intl.formatMessage({ id: 'report.purchase.overview.purchaseNum' }),
});
type ViewType = { key: string; title: string; value: string };
type TrendType = { date: string; value: number | null; type: string };

const PurchaseReport = () => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const goodsActionRef = useRef<ActionType>();
  const [viewList, setViewList] = useState<ViewType[]>([
    {
      key: 'purchaseAmount',
      title: intl.formatMessage({ id: 'report.purchase.overview.purchaseAmount' }),
      value: '-',
    },
    {
      key: 'purchaseOrderNum',
      title: intl.formatMessage({ id: 'report.purchase.overview.purchaseOrderNum' }),
      value: '-',
    },
    {
      key: 'purchaseNum',
      title: intl.formatMessage({ id: 'report.purchase.overview.purchaseNum' }),
      value: '-',
    },
  ]);

  const { hasButtonPerms } = useAccess();

  const [goodsFactor, setGoodsFactor] = useState<string>('price');
  const [purchaseAmountData, setPurchaseAmountData] = useState<TrendType[]>([]);
  const [purchaseNumData, setPurchaseNumData] = useState<TrendType[]>([]);
  const [queryParams, setQueryParams] = useState<QueryPostListRequest>({
    startTime: dayjs().subtract(29, 'days').startOf('day').format('YYYY-MM-DD'),
    endTime: dayjs().endOf('day').format('YYYY-MM-DD'),
  });

  const [purchaseGroupByBrandPieData, setPurchaseGroupByBrandPieData] = useState<PieType[]>([]);
  const [purchaseGroupByCategoryPieData, setPurchaseGroupByCategoryPieData] = useState<PieType[]>(
    [],
  );
  const [purchaseGroupByBrand, setPurchaseGroupByBrand] = useState({
    orderByPurchaseAmountList: [],
    orderByPurchaseNumList: [],
  });
  const [purchaseGroupByCategory, setPurchaseGroupByCategory] = useState({
    orderByPurchaseAmountList: [],
    orderByPurchaseNumList: [],
  });

  useAsyncEffect(async () => {
    await queryPurchaseReport(queryParams);
  }, []);

  const queryPurchaseOverview = async (values: QueryPostListRequest) => {
    const purchaseOverview = await getPurchaseOverview({ ...values });
    if (!isEmpty(purchaseOverview)) {
      //采购概览
      const viewObjList: ViewType[] = [];
      const titleMap = getTitleMap(intl);
      forIn(titleMap, (value, key) => {
        viewObjList.push({
          key,
          title: value,
          value: defaultTo(purchaseOverview?.[key], '-'),
        });
      });
      setViewList(viewObjList);
    }
  };

  const queryPurchaseTrendListData = async (values: QueryPostListRequest) => {
    const purchaseTrendList = await queryPurchaseTrendList({ ...values });
    //采购趋势
    const purchaseAmountDataList: TrendType[] = [];
    const purchaseNumDataList: TrendType[] = [];
    if (purchaseTrendList && purchaseTrendList.length > 0) {
      forEach(purchaseTrendList, (item) => {
        const { bizTime, purchaseAmount, purchaseNum } = item;
        purchaseAmountDataList.push({
          type: intl.formatMessage({ id: 'report.purchase.trends.purchaseAmount' }),
          value: purchaseAmount ?? 0,
          date: bizTime,
        });
        purchaseNumDataList.push({
          type: intl.formatMessage({ id: 'report.purchase.trends.purchaseNum' }),
          value: purchaseNum ?? 0,
          date: bizTime,
        });
      });
    }
    setPurchaseAmountData(purchaseAmountDataList);
    setPurchaseNumData(purchaseNumDataList);
  };

  const queryPurchaseGroup = async (values: QueryPostListRequest) => {
    const purchaseGroupByBrandObj = await getPurchaseGroupByBrand({ ...values });
    const purchaseGroupByCategoryObj = await getPurchaseGroupByCategory({ ...values });
    setPurchaseGroupByBrand(purchaseGroupByBrandObj ?? {});
    setPurchaseGroupByCategory(purchaseGroupByCategoryObj ?? {});
    groupByGoods(goodsFactor, purchaseGroupByBrandObj ?? {}, purchaseGroupByCategoryObj ?? {});
  };

  const queryPurchaseReport = (values: QueryPostListRequest) => {
    queryPurchaseOverview(values);
    queryPurchaseTrendListData(values);
    queryPurchaseGroup(values);
  };
  //查询
  const onSearch = async (values: QueryPostListRequest) => {
    setQueryParams(values);
    await queryPurchaseReport(values);
    actionRef.current?.reload(true);
    goodsActionRef.current?.reload(true);
  };

  //按商品统计
  const groupByGoods = async (
    type: string,
    purchaseGroupByBrandObj,
    purchaseGroupByCategoryObj,
  ) => {
    setGoodsFactor(type);
    let purchaseGroupByBrandPieDataList = [];
    let purchaseGroupByCategoryPieDataList = [];
    switch (type) {
      case 'price':
        purchaseGroupByBrandPieDataList = purchaseGroupByBrandObj.orderByPurchaseAmountList
          ? purchaseGroupByBrandObj.orderByPurchaseAmountList.map((item) => {
              return {
                type: item.brandName,
                value: item.brandPurchaseAmount,
                percent: item.brandPurchaseAmountRate,
              };
            })
          : [];
        setPurchaseGroupByBrandPieData(purchaseGroupByBrandPieDataList);
        purchaseGroupByCategoryPieDataList = purchaseGroupByCategoryObj.orderByPurchaseAmountList
          ? purchaseGroupByCategoryObj.orderByPurchaseAmountList.map((item) => {
              return {
                type: item.categoryName,
                value: item.categoryPurchaseAmount,
                percent: item.categoryPurchaseAmountRate,
              };
            })
          : [];
        setPurchaseGroupByCategoryPieData(purchaseGroupByCategoryPieDataList);
        break;
      case 'amount':
        purchaseGroupByBrandPieDataList = purchaseGroupByBrandObj.orderByPurchaseNumList
          ? purchaseGroupByBrandObj.orderByPurchaseNumList.map((item) => {
              return {
                type: item.brandName,
                value: item.brandPurchaseNum,
                percent: item.brandPurchaseNumRate,
              };
            })
          : [];
        setPurchaseGroupByBrandPieData(purchaseGroupByBrandPieDataList);
        purchaseGroupByCategoryPieDataList = purchaseGroupByCategoryObj.orderByPurchaseNumList
          ? purchaseGroupByCategoryObj.orderByPurchaseNumList.map((item) => {
              return {
                type: item.categoryName,
                value: item.categoryPurchaseNum,
                percent: item.categoryPurchaseNumRate,
              };
            })
          : [];
        setPurchaseGroupByCategoryPieData(purchaseGroupByCategoryPieDataList);
        break;
    }
  };

  const config: DualAxesConfig = {
    xField: 'date',
    yField: 'value',
    legend: {
      color: {
        layout: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      },
    },
    children: [
      {
        seriesField: 'type',
        colorField: 'type',
        data: [...purchaseAmountData, ...purchaseNumData],
        type: 'line',
        shapeField: 'smooth',
        axis: {
          x: {
            size: purchaseAmountData.length > 20 ? 0 : 120,
          },
          y: {
            position: 'left',
            title: intl.formatMessage({ id: 'report.purchase.axis.yuan' }),
            titlePosition: 'top',
          },
        },
      },
    ],
  };
  let pConfig: Partial<PageContainerProps> = {};
  //@ts-ignore
  if (isFramed()) {
    pConfig = PageConfig(hasButtonPerms, intl);
  }

  return (
    <ConfigProvider
      //@ts-ignore
      theme={isFramed() ? miniMpAntdTheme : null}
    >
      <PageContainer {...pConfig} tabActiveKey={location.pathname}>
        <Flex vertical wrap="wrap" gap={16}>
          <ProCard headerBordered>
            <QueryFilter
              onFinish={onSearch}
              submitter={false}
              formRef={formRef}
              className="!p-0"
              defaultColsNumber={4}
            >
              <ProFormSelect
                name="storeIdList"
                label={intl.formatMessage({ id: 'report.purchase.form.store' })}
                mode={'multiple'}
                fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
                request={() => queryStoreByAccount({ status: 1 })}
              />
              <ProFormSelect
                name="supplierIdList"
                label={intl.formatMessage({ id: 'report.purchase.form.supplier' })}
                showSearch
                mode={'multiple'}
                request={async () => {
                  const data = await querySupplierList({});
                  return data?.map(({ supplierId, supplierName }) => ({
                    key: supplierId,
                    value: supplierId,
                    label: supplierName,
                  }));
                }}
              />
              <ProFormText
                label={intl.formatMessage({ id: 'report.purchase.form.goodsInfo' })}
                name="itemFuzzy"
                placeholder={intl.formatMessage({
                  id: 'report.purchase.form.goodsInfoPlaceholder',
                })}
              />
              <ProFormSelect
                label={intl.formatMessage({ id: 'report.purchase.form.brand' })}
                name="brandIdList"
                showSearch={true}
                debounceTime={300}
                mode={'multiple'}
                fieldProps={{
                  filterOption: false,
                  maxTagCount: 3,
                  optionRender: (option) => <Space>{option.data.label}</Space>,
                }}
                request={(query) => {
                  const params: any = {
                    brandStatus: '1',
                    pageSize: 99,
                    pageNo: 1,
                    brandName: query.keyWords,
                  };
                  return queryGoodsPropertyPage(params, 'brand').then((result) =>
                    result.data?.map((item) => ({
                      label: item.brandName,
                      dataType: item.dataType,
                      value: item.brandId,
                    })),
                  );
                }}
              />
              <ProFormTreeSelect
                label={intl.formatMessage({ id: 'report.purchase.form.category' })}
                name="categoryIdList"
                fieldProps={{
                  maxTagCount: 3,
                  treeCheckable: true,
                  filterTreeNode: (text, treeNode) => treeNode.text?.includes(text),
                }}
                request={() => {
                  const params: any = {
                    categoryStatus: 1,
                    pageSize: 999,
                    pageNo: 1,
                    isReturnTree: true,
                  };
                  return queryGoodsPropertyPage(params, 'category').then((result) =>
                    transformCategoryTree(result.data),
                  );
                }}
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.purchase.form.startTime' })}
                initialValue={dayjs().subtract(29, 'days').startOf('day')}
                allowClear={false}
                name="startTime"
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.purchase.form.endTime' })}
                initialValue={dayjs().endOf('day')}
                allowClear={false}
                name="endTime"
              />
              <Flex align="center" gap={8}>
                <Button type="primary" htmlType="submit">
                  {intl.formatMessage({ id: 'report.purchase.form.query' })}
                </Button>
                <Button htmlType="reset">
                  {intl.formatMessage({ id: 'report.purchase.form.reset' })}
                </Button>
              </Flex>
            </QueryFilter>
          </ProCard>
          <ProCard headerBordered>
            <Flex wrap="wrap" className="mt-6 px-6" gap={16} justify="space-between">
              {viewList &&
                viewList.map((viewItem) => (
                  <div key={viewItem.title} className="flex flex-col">
                    <span className="text-[#00000073]">
                      <span className="mr-2">{viewItem.title}</span>
                    </span>
                    <span className="text-[#000000D9] text-[24px] font-semibold mt-1 mb-2 w-40">
                      <NumberFormatText
                        precision={viewItem.key == 'purchaseAmount' ? 2 : 0}
                        numberText={viewItem.value}
                      />
                    </span>
                  </div>
                ))}
            </Flex>
          </ProCard>
          <ProCard
            title={
              <SubTitle text={intl.formatMessage({ id: 'report.purchase.charts.purchaseTrend' })} />
            }
          >
            <DualAxes {...config} />
          </ProCard>
          <ProCard
            title={
              <SubTitle
                text={intl.formatMessage({ id: 'report.purchase.charts.supplierStatistics' })}
              />
            }
          >
            <FunProTable<QueryPostListEntity, any>
              ghost={true}
              rowKey="supplierId"
              actionRef={actionRef}
              options={false}
              search={false}
              scroll={{ x: 'max-content' }}
              params={{
                ...queryParams,
                groupType: GroupType.PURCHASE_REPORT_GROUP_BY_SUPPLIER,
              }}
              requestPage={async (params, sort) => {
                if (params.startTime && params.endTime) {
                  const sortField = Object.values(sort)[0];
                  let sortType;
                  if (sortField) {
                    sortType = sortField === 'ascend' ? 'ASC' : 'DESC';
                  } else {
                    sortType = undefined;
                  }
                  return await queryPurchaseReportGroupPage({
                    ...params,
                    sortBy: Object.keys(sort)[0] ?? undefined,
                    sortType,
                  });
                }
                return { data: [], total: 0 };
              }}
              columns={PostListTableColumns()}
            />
          </ProCard>
          <ProCard
            gutter={8}
            title={
              <SubTitle
                text={intl.formatMessage({ id: 'report.purchase.charts.goodsStatistics' })}
              />
            }
            extra={
              <Radio.Group
                value={goodsFactor}
                onChange={(e) =>
                  groupByGoods(e.target.value, purchaseGroupByBrand, purchaseGroupByCategory)
                }
              >
                <Radio.Button value="price">
                  {intl.formatMessage({ id: 'report.purchase.radio.purchaseAmount' })}
                </Radio.Button>
                <Radio.Button value="amount">
                  {intl.formatMessage({ id: 'report.purchase.radio.purchaseNum' })}
                </Radio.Button>
              </Radio.Group>
            }
            split="horizontal"
          >
            <ProCard gutter={8} split="vertical" wrap>
              <ProCard
                colSpan={{ xs: 24, md: 12 }}
                title={intl.formatMessage({ id: 'report.purchase.charts.brandChart' })}
              >
                <PieCharts data={purchaseGroupByBrandPieData} />
              </ProCard>
              <ProCard
                colSpan={{ xs: 24, md: 12 }}
                title={intl.formatMessage({ id: 'report.purchase.charts.categoryChart' })}
              >
                <PieCharts data={purchaseGroupByCategoryPieData} />
              </ProCard>
            </ProCard>
            <ProCard>
              <FunProTable<QueryPostListEntity, any>
                ghost={true}
                actionRef={goodsActionRef}
                options={false}
                search={false}
                scroll={{ x: 'max-content' }}
                params={{
                  ...queryParams,
                  groupType: GroupType.PURCHASE_REPORT_GROUP_BY_ITEM,
                }}
                requestPage={async (params, sort) => {
                  if (params.startTime && params.endTime) {
                    const sortField = Object.values(sort)[0];
                    let sortType;
                    if (sortField) {
                      sortType = sortField === 'ascend' ? 'ASC' : 'DESC';
                    } else {
                      sortType = undefined;
                    }
                    return await queryPurchaseReportGroupPage({
                      ...params,
                      sortBy: Object.keys(sort)[0] ?? undefined,
                      sortType,
                    });
                  }
                  return { data: [], total: 0 };
                }}
                columns={PostListForGoodsTableColumns()}
              />
            </ProCard>
          </ProCard>
        </Flex>
      </PageContainer>
    </ConfigProvider>
  );
};
export default withKeepAlive(PurchaseReport);
