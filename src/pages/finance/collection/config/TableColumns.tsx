import MutiCurrency from '@/components/common/MutiCurrency';
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { OverdueStatusValueEnum } from '@/types/CommonStatus';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { type FinReceivableEntity } from '../types/FinReceivableEntity.entity';

export const getTableColumns = (
  intl: IntlShape,
  form: ProFormInstance,
): ProColumns<FinReceivableEntity>[] => [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      // 应付对象
      title: intl.formatMessage({ id: 'finance.collection.columns.customer' }),
      dataIndex: 'buyerId',
      width: 160,
      ellipsis: true,
      hideInTable: true,
      renderFormItem: (text, record) => {
        return (
          <>
            <ProFormObject
              form={form}
              fieldsName={{
                fieldType: 'buyerType',
                fieldName: 'buyerName',
                fieldId: 'buyerId',
              }}
              objects={[ObjectType.Customer, ObjectType.OtherCompany]}
            />
          </>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.customer' }),
      dataIndex: 'buyerName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.store' }),
      dataIndex: 'storeName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.store' }),
      dataIndex: 'storeIdList',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.overdueStatus' }),
      dataIndex: 'overdueFlag',
      width: 120,
      fixed: 'left',
      valueType: 'select',
      valueEnum: OverdueStatusValueEnum,
      hideInTable: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.receivableAmount' }),
      dataIndex: 'totalRecAmountList',
      width: 140,
      search: false,
      render: (_, record) => <MutiCurrency amountList={record?.totalRecAmountList} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.overdueAmount' }),
      dataIndex: 'overdueAmountYuan',
      valueType: 'money',
      width: 140,
      search: false,
      render: (_, record) => <MutiCurrency amountList={record?.overdueAmountList} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.debtAmount' }),
      dataIndex: 'totalDebtAmountList',
      width: 140,
      search: false,
      render: (_, record) => <MutiCurrency amountList={record?.totalDebtAmountList} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.overDueAmount' }),
      dataIndex: 'totalOverDueAmountList',
      width: 140,
      search: false,
      render: (_, record) => <MutiCurrency amountList={record?.totalOverDueAmountList} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.totalAmount' }),
      dataIndex: 'totalAmountYuan',
      // valueType: 'money',
      width: 100,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.availableAmount' }),
      dataIndex: 'availableAmountYuan',
      // valueType: 'money',
      width: 100,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.settleType' }),
      dataIndex: 'creditTermsType',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.overdueStatus' }),
      dataIndex: 'status',
      width: 120,
      valueType: 'select',
      search: false,
      valueEnum: OverdueStatusValueEnum,
    },
  ];
