import { auditCostFlow, confirmCostFlow, queryCostFlowDetail } from '@/pages/finance/cost/services';
import {
  FinanceCostStateEnum,
  FinanceCostStateEnumMap,
  FinanceCostTypeEnumMap,
} from '@/pages/finance/cost/types/FinanceCost.enum';
import type { FinanceCostEntity } from '@/pages/finance/cost/types/FinanceCostEntity.entity';
import { ProCard, ProDescriptions, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, Button, Drawer, Flex, Space, Tag } from 'antd';
import { FC, useEffect, useState } from 'react';
import { DetailDrawerProps } from './IncomeDetailDrawer';

const ExpendDetailDrawer: FC<DetailDrawerProps> = ({ open, onClose, entity, actionRef }) => {
  const intl = useIntl();
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<FinanceCostEntity | null>(null);

  const fetchDetail = async () => {
    setLoading(true);
    try {
      const res = await queryCostFlowDetail({ id: entity?.id });
      setDetail(res);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && entity.id) {
      fetchDetail();
    }
  }, [open, entity?.id]);

  const handleAudit = async (auditState: number) => {
    setLoading(true);
    try {
      const result = await auditCostFlow({ id: entity.id, status: auditState });
      if (result) {
        message.success(intl.formatMessage({ id: 'common.message.operation.success' }));
        actionRef?.current?.reload();
        onClose?.();
      }
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = async () => {
    const result = await confirmCostFlow({ id: entity?.id });
    if (result) {
      message.success(intl.formatMessage({ id: 'common.message.operation.success' }));
      actionRef?.current?.reload();
      onClose?.();
    }
  };

  const columns: ProDescriptionsItemProps<FinanceCostEntity>[] = [
    {
      title: intl.formatMessage({ id: 'finance.cost.form.expendStore' }),
      dataIndex: 'storeName',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendNature' }),
      dataIndex: 'ledgerType',
      valueEnum: FinanceCostTypeEnumMap,
      valueType: 'select',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendObject' }),
      dataIndex: 'objName',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendType' }),
      dataIndex: 'tagType',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.creator' }),
      dataIndex: 'createPerson',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.createTime' }),
      dataIndex: 'createTime',
    },

    {
      title: intl.formatMessage({ id: 'finance.collection.columns.currency' }),
      dataIndex: 'currency',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.incomeAmount' }),
      dataIndex: 'amountYuan',
      valueType: 'money',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.settlementAccount' }),
      dataIndex: 'accountName',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.form.pic' }),
      dataIndex: 'pic',
      valueType: 'image',
    },
    {
      title: intl.formatMessage({ id: 'common.column.remark' }),
      dataIndex: 'remark',
    },
  ];

  return (
    <Drawer
      open={open}
      onClose={onClose}
      width={800}
      title={intl.formatMessage(
        { id: 'finance.cost.detailTitle' },
        { type: intl.formatMessage({ id: 'finance.cost.type.expend' }) },
      )}
      bodyStyle={{
        backgroundColor: '#efefef',
      }}
      footer={
        <Flex justify="end" gap={20}>
          <Space>
            {detail?.status === FinanceCostStateEnum.PENDING && (
              <>
                {/* <ModalForm
                  title={intl.formatMessage({ id: 'finance.receive.audit.reject' })}
                  trigger={<Button type="primary" ghost>{intl.formatMessage({ id: 'finance.receive.audit.reject' })}</Button>}
                  onFinish={async (values) => {
                    await handleAudit(FinanceCostStateEnum.ABORTED, values.remark);
                    return true;
                  }}
                >
                  <Form.Item name="remark">
                    <Input.TextArea rows={4} placeholder={intl.formatMessage({ id: 'finance.receive.audit.reject.reason.placeholder' })} />
                  </Form.Item>
                </ModalForm> */}
                <Button
                  onClick={() => {
                    handleAudit(FinanceCostStateEnum.ABORTED);
                  }}
                >
                  {intl.formatMessage({ id: 'finance.receive.audit.reject' })}
                </Button>
                <Button type="primary" onClick={handleConfirm}>
                  {intl.formatMessage({ id: 'finance.receive.audit.approve' })}
                </Button>
              </>
            )}
          </Space>
        </Flex>
      }
    >
      <ProCard className="rounded-lg">
        <ProDescriptions
          loading={loading}
          dataSource={detail}
          title={
            <Space>
              <span>{detail?.serialNumber}</span>
              <Tag color={FinanceCostStateEnumMap[detail?.status as FinanceCostStateEnum]?.color}>
                {FinanceCostStateEnumMap[detail?.status as FinanceCostStateEnum]?.text}
              </Tag>
            </Space>
          }
          column={3}
          columns={columns}
        />
      </ProCard>
    </Drawer>
  );
};

export default ExpendDetailDrawer;
