import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard, ProColumns } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space, Tabs } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CurrencyManager from './components/CurrencyManager';
import FinancePropertyFormModal from './components/FinancePropertyFormModal';
import { getTagTableColumns } from './config/TagTableColumns';
import { createFinTag, queryFinanceTagPage, updateFinTag } from './services';
import type { FinanceTagEntity } from './types/FinanceTagEntity.entity';

const FinanceTag = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [activeKey, setActiveKey] = useState('tag');

  const [columns, setColumns] = useState<ProColumns<FinanceTagEntity>[]>();

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    const operatorColumn: ProColumns<FinanceTagEntity> = {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      width: 150,
      render: (_text, record, _, action) => {
        const { source } = record;
        if (source === 0) {
          return null;
        }
        return (
          <AuthButton
            isHref
            authority="editFinTag"
            key="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
        );
      },
    };
    const newColumns = getTagTableColumns({ intl });
    setColumns(() => [...newColumns, operatorColumn]);
  }, [intl]);

  const [createModalProps, setCreateModalProps] = useState<PropertyModalFromType<number>>({
    inputFieldLabel: '',
    inputFieldName: '',
    onCancel: () => {},
    onOk: () => Promise.resolve(undefined),
    visible: false,
    recordId: 0,
    readOnly: false,
    title: intl.formatMessage({ id: 'finance.tag.addIncomeExpenseType' }),
  });

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
  };

  /**
   * 新增
   * @param values
   */
  const handleSaveOrUpdate = async (values: any) => {
    try {
      const result = await createFinTag({ ...values });
      if (result) {
        hideModal();
        actionRef.current?.reset?.();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  /**
   * 编辑-保存
   * @param _key
   * @param record
   */
  const onEditSave = async (_key: React.Key | React.Key[], record: FinanceTagEntity) => {
    const result = await updateFinTag(record);
    if (!result) {
      return Promise.reject();
    }
    return true;
  };

  const items = [
    {
      key: 'tag',
      label: intl.formatMessage({ id: 'finance.tag.title' }),
      children: (
        <FunProTable<FinanceTagEntity, any>
          editable={{
            type: 'single',
            onSave: onEditSave,
            actionRender: (_row, _config, defaultDom) => [defaultDom.save, defaultDom.cancel],
          }}
          rowKey="id"
          form={{
            className: '!p-0',
          }}
          ghost={true}
          scroll={{ x: 'max-content' }}
          requestPage={queryFinanceTagPage}
          actionRef={actionRef}
          columns={columns}
          headerTitle={
            <Space>
              <AuthButton
                authority="addFinTag"
                type="primary"
                key="create"
                onClick={() => {
                  setCreateModalProps((preModalProps) => ({
                    ...preModalProps,
                    visible: true,
                    readOnly: false,
                    title: intl.formatMessage({ id: 'finance.tag.addIncomeExpenseType' }),
                  }));
                }}
              >
                {intl.formatMessage({ id: 'finance.tag.addIncomeExpenseType' })}
              </AuthButton>
            </Space>
          }
        />
      ),
    },
    {
      key: 'currency',
      label: intl.formatMessage({ id: 'finance.currency.title' }),
      children: <CurrencyManager />,
    },
  ];

  return (
    <PageContainer>
      <ProCard tabs={{ items, activeKey, onChange: setActiveKey }} />
      <FinancePropertyFormModal
        {...createModalProps}
        onCancel={hideModal}
        onOk={handleSaveOrUpdate}
      />
    </PageContainer>
  );
};

export default withKeepAlive(FinanceTag);
