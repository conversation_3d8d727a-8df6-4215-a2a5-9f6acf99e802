import FunProTable from '@/components/common/FunProTable';
import type { ProColumns } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { getCurrencyTableColumns } from '../config/CurrencyTableColumns';
import {
  queryAllCurrency,
  saveCurrencyExchangeRate,
  updateCurrencyExchangeRate,
} from '../services';
import type { CurrencyEntity } from '../types/CurrencyEntity';

const CurrencyManager = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [columns, setColumns] = useState<ProColumns<CurrencyEntity>[]>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    const operatorColumn: ProColumns<CurrencyEntity> = {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      width: 150,
      renderText: (_text, record, _, action) => {
        if (record.isBaseCurrency) {
          return null;
        }
        return (
          <a
            key="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </a>
        );
      },
    };
    const newColumns = getCurrencyTableColumns({ intl });
    setColumns(() => [...newColumns, operatorColumn]);
  }, [intl]);

  const onEditSave = async (key: React.Key | React.Key[], record: CurrencyEntity) => {
    try {
      let result;
      if (String(record.id).startsWith('new-')) {
        result = await saveCurrencyExchangeRate({
          currencyName: record.targetCurrency,
          currencySymbol: record.currencySymbol,
          rate: record.rate,
          isActive: record.isActive,
        });
      } else {
        result = await updateCurrencyExchangeRate({
          currencyName: record.targetCurrency,
          currencySymbol: record.currencySymbol,
          rate: record.rate,
          id: record.id,
          isActive: record.isActive,
        });
      }
      if (result) {
        message.success(intl.formatMessage({ id: 'common.message.save.success' }));
        return true;
      }
      actionRef.current?.reload();
      return false;
    } catch (error) {
      return false;
    }
  };

  const handleAdd = () => {
    const newId = `new-${Math.random().toString(36).substring(2, 11)}`;
    actionRef.current?.addEditRecord?.({
      id: newId,
    });
    setEditableRowKeys([newId]);
  };

  return (
    <FunProTable<CurrencyEntity, any>
      editable={{
        type: 'multiple',
        editableKeys,
        onSave: onEditSave,
        onChange: setEditableRowKeys,
        actionRender: (_row, _config, defaultDom) => {
          return [defaultDom.save, defaultDom.cancel];
        },
      }}
      ghost={true}
      rowKey="id"
      scroll={{ x: 'max-content' }}
      request={async () => {
        const result = await queryAllCurrency({});
        return {
          data: result,
          success: true,
        };
      }}
      actionRef={actionRef}
      columns={columns}
      search={false}
      pagination={false}
      headerTitle={
        <Space>
          <Button type="primary" key="create" onClick={handleAdd}>
            {intl.formatMessage({ id: 'finance.currency.add' })}
          </Button>
        </Space>
      }
    />
  );
};

export default CurrencyManager;
