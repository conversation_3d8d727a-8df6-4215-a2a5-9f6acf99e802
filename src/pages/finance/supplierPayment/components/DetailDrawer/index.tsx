import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { MoneyFormat } from '@/pages/finance/receive';
import { queryPaymentFlowList } from "@/pages/finance/supplierPayment/services";
import type { FinPaymentEntity } from "@/pages/finance/supplierPayment/types/FinPaymentEntity";
import type { FinPaymentFlowEntity } from "@/pages/finance/supplierPayment/types/FinPaymentFlowEntity";
import type { PaymentDetailModalType } from "@/pages/finance/supplierPayment/types/PaymentDetailModalType";
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { useState } from "react";
import { DetailColumns } from '../../config/DetailColumns';

export default (props: PaymentDetailModalType) => {
  const intl = useIntl();
  const [finPaymentRo, setFinPaymentRo] = useState<FinPaymentEntity>(null);
  const [finPaymentFlowRoList, setFinPaymentFlowRoList] = useState<FinPaymentFlowEntity[]>([]);

  useAsyncEffect(async () => {
    if (!props.serialNumber) {
      return;
    }
    const paymentFlow = await queryPaymentFlowList(props.serialNumber);
    setFinPaymentRo(paymentFlow?.finPaymentRo);
    setFinPaymentFlowRoList(
      paymentFlow?.finPaymentFlowRoList?.map(flow => ({
        ...flow,
        storeName: paymentFlow?.finPaymentRo?.storeName,
      }))
    );
  }, [props.visible]);

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={false}
    >
      <ProCard className="mb-4">
        <ProDescriptions
          title={props.serialNumber}
          column={3}
          dataSource={finPaymentRo}
          columns={[
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.supplierName' }),
              dataIndex: 'sellerName',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentStore' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentTime' }),
              dataIndex: 'payTime',
            },
            {
              title: intl.formatMessage({ id: 'common.field.currency' }),
              dataIndex: 'currency',
            },
            {
              title: intl.formatMessage({ id: 'common.field.rate' }),
              dataIndex: 'rate',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAccount' }),
              dataIndex: 'paymentAccountName',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAmount' }),
              dataIndex: 'totalPaymentAmountYuan',
              render: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.adjustAmount' }),
              dataIndex: 'adjustAmountYuan',
              render: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.currentWriteOff' }),
              dataIndex: 'writeOffAmountYuan',
              render: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.lossAmount' }),
              dataIndex: 'lossAmountYuan',
              render: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.creator' }),
              dataIndex: 'createPerson',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.businessTime' }),
              dataIndex: 'businessTime',
            },
            {
              title: 'Reference No.',
              dataIndex: 'refNo',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.image' }),
              dataIndex: 'pic',
              valueType: 'image',
            },
            {
              title: intl.formatMessage({ id: 'finance.supplierPayment.columns.remark' }),
              dataIndex: 'remark',
            },
          ]}
        />
      </ProCard>
      <FunProTable<FinPaymentFlowEntity, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'finance.supplierPayment.writeOffOrder' })} />}
        scroll={{ x: 'max-content' }}
        search={false}
        pagination={false}
        dataSource={finPaymentFlowRoList}
        options={false}
        columns={DetailColumns}
      />
    </DrawerForm>
  );
};
