import downloadIcon from '@/assets/icons/icon_download.png';
import importIcon from '@/assets/icons/icon_import.png';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { createImportTask, downloadFile } from '@/services/systerm';
import { calcImportParams } from '@/utils/importData';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { history } from '@@/core/history';
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form, Image, Modal, Space, Upload } from 'antd';

const ACCEPT_FILE_EXT = '.xls,.xlsx,.csv';

export interface BatchImportModalProps {
  visible: boolean;
  onCancel?: () => void;
}

const BatchImportModal: React.FC<BatchImportModalProps> = (props) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const fileImportParams = {
    systemId: 'GRIPX_STORE_SYS',
    taskDesc: intl.formatMessage({ id: 'finance.receive.batchImport' }),
    moduleId: 'RECEIVED_IMPORT',
  };

  const handleUploadOssSuccess = async (url) => {
    const valid = await form.validateFields();
    if (!valid) {
      return;
    }
    const values = form.getFieldsValue();
    const params = {
      ...values,
      receiveTime: values.receiveTime.format('YYYY-MM-DD HH:mm:ss'),
      url,
    };
    const formatParams = calcImportParams(params);

    createImportTask({ ...fileImportParams, params: formatParams }).then((result) => {
      if (result?.taskId) {
        props.onCancel?.();

        Modal.success({
          title: intl ? intl.formatMessage({ id: 'common.import.successTitle' }) : 'Notice',
          centered: true,
          content: intl
            ? intl.formatMessage({ id: 'common.import.successContent' })
            : 'Import task has been created successfully. Do you want to view the import results?',
          okText: intl ? intl.formatMessage({ id: 'common.import.successOkText' }) : 'View Results',
          onOk: () => {
            history.push('/system/job?jobType=Import');
          },
          closable: true,
        });
      }
    });
  };

  return (
    <ModalForm
      title={intl.formatMessage({ id: 'finance.receive.batchAdd' })}
      open={props.visible}
      layout="vertical"
      form={form}
      onVisibleChange={props.onCancel}
      width={480}
      submitter={false}
    >
      <div className="pb-6">
        <ProFormSelect
          name="receiveStoreId"
          label={intl.formatMessage({ id: 'finance.receive.receiveStore' })}
          required
          rules={[REQUIRED_RULES]}
          request={async () => {
            const data = await queryStoreByAccount({});
            if (data && data.length > 0) {
              form.setFieldsValue({
                receiveStoreId: data[0].id,
                receiveStoreName: data[0].name,
              });
            }
            return data?.map(({ id, name }) => ({
              value: id,
              label: name,
            }));
          }}
        />
        <ProFormText hidden name="receiveStoreName" />

        <ProFormSelect
          name="receivedAccountId"
          label={intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' })}
          required
          rules={[REQUIRED_RULES]}
          request={async () => {
            const data = await queryMemberAccountPage({
              pageSize: 1000,
              currency: 'AUD',
            });
            return data?.data
              ?.map((t) => ({
                ...t,
                label: t.memberAccountName,
                value: t.id,
              }))
              .filter((item) => {
                return ![3, 4].includes(item.accountType);
              });
          }}
        />
        <ProFormText hidden name="receivedAccountName" />

        <ProFormDateTimePicker
          width={'100%'}
          name={'receiveTime'} label={intl.formatMessage({ id: 'finance.receive.columns.receiveTime' })} rules={[{ required: true }]}
          fieldProps={{
            showTime: true,
            format: 'DD/MM/YYYY HH:mm:ss',
          }}
        />
        <Space size={40}>
          <div className="cursor-pointer flex justify-center items-center">
            <Image src={downloadIcon} height={24} width={24} preview={false} />
            <span
              className="ml-2 cursor-pointer hover:text-primary transition-all"
              onClick={() =>
                downloadFile(
                  'gie/static/etc-saas/%E6%94%B6%E6%AC%BE%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                )
              }
            >
              {intl.formatMessage({ id: 'common.import.downloadTemplate' })}
            </span>
          </div>
          <Upload
            name="file"
            multiple={false}
            action="/apigateway/public/upload/object/batch"
            accept={ACCEPT_FILE_EXT}
            onChange={(info) => {
              if (info.file.status === 'done') {
                const url = info.file?.response?.data?.[0];
                if (url) {
                  handleUploadOssSuccess(url);
                }
              }
            }}
          >
            <div className="cursor-pointer flex justify-center items-center">
              <Image src={importIcon} height={24} width={24} preview={false} />
              <span className="ml-2 cursor-pointer hover:text-primary transition-all">
                {intl.formatMessage({ id: 'common.import.importFile' })}
              </span>
            </div>
          </Upload>
        </Space>
      </div>
    </ModalForm>
  );
};

export default BatchImportModal;
