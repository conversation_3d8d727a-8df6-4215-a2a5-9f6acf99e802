
export enum AdjustType {
  /**
   * 无
   */
  NONE = 1,
  /**
   * 收款抹零
   */
  ROUND = 2,
  /**
   * 收款优惠
   */
  DISCOUNT = 3,
}
export interface ReceivedConfirmation {
  /**
   * 调整金额
   */
  adjustAmount?: number;
  /**
   * 调整原因
   */
  adjustReason?: string;
  /**
   * 调整类型
   */
  adjustType?: AdjustType;
  /**
   * 买方id
   */
  buyerId?: string;
  /**
   * 买方名称
   */
  buyerName?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 扩展备注
   */
  extRemark?: string;
  /**
   * 实收流水
   */
  finReceivedOrderDetailCmdList?: FinReceivedOrderDetailCmdList[];
  firstName?: string;
  lastName?: string;
  /**
   * 台账类型1是收入2是支出@seecom.ipms.finance.account.api.dto.enums.AccountFlowLedgerTypeEnums
   */
  ledgerType?: number;
  /**
   * 本货币汇率损益
   */
  lossAmount?: number;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 操作人名称
   */
  operatorName?: string;
  /**
   * 操作人id
   */
  operatorNo?: string;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 收款账户id
   */
  receivedAccountId?: string;
  /**
   * 收款账户名称
   */
  receivedAccountName?: string;
  /**
   * 收款时间
   */
  receiveTime?: string;
  /**
   * 收款图片
   */
  receivePic?: string;
  /**
   * 收款门店id
   */
  receiveStoreId?: string;
  /**
   * 收款门店名称
   */
  receiveStoreName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 卖方id
   */
  sellerId?: string;
  /**
   * 卖方名称
   */
  sellerName?: string;
  /**
   * 实收总金额，单位：分
   */
  totalReceivedAmount?: number;
  /**
   * 实收总金额，单位：元
   */
  totalReceivedAmountYuan?: number;
  /**
   * 本次核销金额
   */
  writeOffAmount?: number;
}

export interface FinReceivedOrderDetailCmdList {
  /**
   * None
   */
  extRemark?: string;
  /**
   * None
   */
  firstName?: string;
  /**
   * None
   */
  lastName?: string;
  /**
   * 台账类型1是收入2是支出@seecom.ipms.finance.account.api.dto.enums.AccountFlowLedgerTypeEnums
   */
  ledgerType?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 应收ID
   */
  receivableId?: number;
  /**
   * 收款金额，单位：分
   */
  receivedAmount?: number;
  /**
   * 收款金额，单位：元
   */
  receivedAmountYuan?: number;
}
