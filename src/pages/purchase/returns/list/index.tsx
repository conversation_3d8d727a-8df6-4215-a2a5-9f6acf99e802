import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProFormInstance } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useRef } from 'react';
import { useActivate } from 'react-activation';
import { PostListTableColumns } from './config/postListTableColumns';
import { cancelReturnOrderPost, queryReturnsPagePost, resetReturnOrderPost } from './services';
import { PostEntity } from './types/post.entity';

const PurchaseList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleAgainItem = async (id: string) => {
    history.push(`/purchase/returns/operation?returnOrderId=${id}`);
  };

  /**
   * 作废
   * @param id
   */
  const handleCancelItem = async (id: string, orderNo: string) => {
    const result = await cancelReturnOrderPost({ orderId: id, orderNo });
    if (result) {
      actionRef.current?.reload(true);
    }
  };
  /**
   * 撤回
   * @param id
   * @param orderNo
   */
  const handleRestItem = async (id: string, orderNo: string) => {
    const result = await resetReturnOrderPost({ orderId: id, orderNo });
    if (result) {
      actionRef.current?.reload(true);
    }
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="id"
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        formRef={formRef}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              authority="addPurchaseReturn"
              key="primary"
              onClick={() => {
                history.push('/purchase/returns/operation');
              }}
            >
              {intl.formatMessage({ id: 'purchase.returns.list.button.add' })}
            </AuthButton>
            <AuthButton
              key="export"
              authority="exportPurchaseReturn"
              onClick={() => {
                exportData({
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: intl.formatMessage({ id: 'purchase.returns.list.export.taskDesc' }),
                  moduleId: 'PURCHASE_RETURN_ORDER_EXPORT',
                  params: formRef.current?.getFieldsFormatValue?.(),
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.returns.list.button.export' })}
            </AuthButton>
          </Space>
        }
        requestPage={queryReturnsPagePost}
        columns={PostListTableColumns({
          intl,
          handleAgainItem,
          handleCancelItem,
          handleRestItem,
          formRef,
        })}
      />
    </PageContainer>
  );
};
export default withKeepAlive(PurchaseList);
