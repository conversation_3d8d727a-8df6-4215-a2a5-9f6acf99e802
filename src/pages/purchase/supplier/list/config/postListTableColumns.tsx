import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space } from 'antd';
import { YesNoStatus } from '../../operation/types/YesNo';
import { postStatusOptions, setStatusValue, statusAttribute } from '../types/PostStatus';
import type { SupplierPostEntity } from '../types/supplier.post.entity';

export interface PostListTableColumnsProps {
  intl: any;
  handleDeleteItem: (id: string, status: string) => void;
  handleUpdateItem: (id: string) => void;
  setSupplierDetailDrawerProps: (props: any) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.supplierCode' }),
      dataIndex: ['supplierInfo', 'supplierCode'],
      key: 'supplierCode',
      search: false,
      width: 100,
      render: (_, record) => (
        <a
          onClick={() => {
            props.setSupplierDetailDrawerProps({
              open: true,
              supplierId: record?.supplierInfo?.id,
            });
          }}>
          {_}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.supplierInfo' }),
      dataIndex: 'supplierFuzzy',
      key: 'supplierFuzzy',
      hideInTable: true,
      fieldProps: { placeholder: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.supplierInfo.placeholder' }) },
      search: true,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.supplierName' }),
      dataIndex: ['supplierInfo', 'supplierName'],
      key: 'supplierName',
      search: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.abn' }),
      dataIndex: ['supplierInfo', 'abn'],
      key: 'abn',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.settleType' }),
      dataIndex: ['supplierSettleInfo', 'settleType'],
      key: 'settleType',
      search: false,
      width: 120,
    },
    {
      title: 'Country',
      dataIndex: ['supplierInfo', 'country'],
      key: 'country',
      search: false,
      width: 120,
    },
    {
      title: 'Suburb/City',
      dataIndex: ['supplierAddressList', 0, 'detailAddress'],
      key: 'country',
      search: false,
      width: 120,
      renderText: (text, record) => {
        const rowData = record?.supplierAddressList?.[0];
        if (rowData?.province || rowData?.area) {
          return (`${rowData.province ?? ''} / ${rowData.area ?? ''}`);
        } else {
          return '-';
        }
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.financialEmail' }),
      dataIndex: ['supplierInfo', 'financeEmail'],
      key: 'country',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.defaultContact' }),
      dataIndex: ['supplierConcatList', 0, 'concatPerson'],
      key: 'concatPerson',
      search: true,
      width: 120,
      formItemProps: {
        label: props.intl.formatMessage({ id: 'purchase.supplier.list.search.contact' }),
      },
      ellipsis: true,
      renderText: (text, record) => {
        const { supplierConcatList } = record;
        return `${supplierConcatList?.[0]?.firstName ?? ''} ${supplierConcatList?.[0]?.lastName ?? ''}`;
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.defaultContactPhone' }),
      dataIndex: ['supplierConcatList', 0, 'concatPhone'],
      key: 'concatPhone',
      width: 100,
      search: true,
      formItemProps: {
        label: props.intl.formatMessage({ id: 'purchase.supplier.list.search.contactPhone' }),
      },
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.defaultAddress' }),
      dataIndex: ['supplierAddressList', 0, 'detailAddress'],
      key: 'detailAddress',
      search: false,
      width: 140,
      ellipsis: true,
    },
    // {
    //   title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.createTime' }),
    //   dataIndex: ['supplierInfo', 'createTime'],
    //   key: 'createTime',
    //   search: false,
    //   width: 140,
    // },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.createTime' }),
      dataIndex: 'assginTimeAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.list.columns.status' }),
      dataIndex: ['supplierInfo', 'supplierStatus'],
      key: 'supplierStatus',
      search: false,
      width: 80,
      valueEnum: postStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record: SupplierPostEntity) => {
        return (
          YesNoStatus.YES == record.supplierInfo?.canEdit && (
            <Space>
              <AuthButton
                isHref
                authority="editSupplier"
                onClick={() => props.handleUpdateItem(record?.supplierInfo?.id ?? '')}
              >
                {props.intl.formatMessage({ id: 'purchase.supplier.list.button.edit' })}
              </AuthButton>
              <Popconfirm
                title={props.intl.formatMessage(
                  { id: 'purchase.supplier.list.confirm.statusChange' },
                  { action: props.intl.formatMessage({ id: statusAttribute[record?.supplierInfo?.supplierStatus ?? ''] }) }
                )}
                onConfirm={() =>
                  props.handleDeleteItem(
                    record?.supplierInfo?.id ?? '',
                    setStatusValue[record?.supplierInfo?.supplierStatus ?? ''],
                  )
                }
              >
                <AuthButton isHref authority="enableOrDisableSupplier">
                  {props.intl.formatMessage({ id: statusAttribute[record?.supplierInfo?.supplierStatus ?? ''] })}
                </AuthButton>
              </Popconfirm>
            </Space>
          )
        );
      },
    },
  ] as ProColumns<SupplierPostEntity>[];
