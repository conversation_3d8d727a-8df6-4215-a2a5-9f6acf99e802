import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { CommonModelForm } from '@/types/CommonModelForm';
import { exportData } from '@/utils/exportData';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProFormInstance } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { isString } from 'lodash';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import SupplierDetailDrawer from '../components/SupplierDetailDrawer';
import Operation from '../operation';
import { AddPostEntity } from '../operation/types/add.post.entity';
import { createOrUpdateFullInfoPost, queryPostList, updateStatusById } from '../services';
import { PostListTableColumns } from './config/postListTableColumns';
import { SupplierPostEntity } from './types/supplier.post.entity';

const SupplierList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const [supplierModalProps, setSupplierModalProps] = useState<CommonModelForm<string, any>>({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });

  const [supplierDetailDrawerProps, setSupplierDetailDrawerProps] = useState<any>({
    open: false,
    supplierId: undefined,
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 禁用事件
   * @param ids
   */
  const handleDeleteItem = async (id: string, supplierStatus: string) => {
    const data = await updateStatusById({ id, supplierStatus });
    if (data) {
      actionRef.current?.reload(true);
    }
  };

  const handleUpdateItem = async (id?: string) => {
    setSupplierModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'purchase.supplier.list.modal.title.edit' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setSupplierModalProps({ visible: false, recordId: '0', readOnly: false, title: '' });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: AddPostEntity) => {
    try {
      if (values?.supplierAddressList) {
        values.supplierAddressList = values.supplierAddressList.map((s) => {
          const { id } = s;
          if (isString(id) && id?.startsWith('new_')) {
            delete s.id;
          }
          return s;
        });
      }
      if (values?.supplierConcatList) {
        values.supplierConcatList = values.supplierConcatList.map((s) => {
          const { id } = s;
          if (isString(id) && id?.startsWith('new_')) {
            delete s.id;
          }
          if (s.postList) {
            delete s.postList;
          }
          return s;
        });
      }
      if (values?.supplierSettleAccountList) {
        values.supplierSettleAccountList = values.supplierSettleAccountList.map((s) => {
          const { id } = s;
          if (isString(id) && id?.startsWith('new_')) {
            delete s.id;
          }
          return s;
        });
      }
      if (values?.supplierSettleInfo) {
        values.supplierSettleInfo.isMultiCurrency = values.supplierSettleInfo.isMultiCurrency
          ? 1
          : 0;
        values.supplierSettleInfo.gstExcluded = values.supplierSettleInfo.gstExcluded ? 1 : 0;
      }
      const { data } = await createOrUpdateFullInfoPost(values);
      if (data) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<SupplierPostEntity, any>
        rowKey={(item) => item.supplierInfo?.id ?? ''}
        requestPage={queryPostList}
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        formRef={formRef}
        columns={PostListTableColumns({
          intl,
          handleDeleteItem,
          handleUpdateItem,
          setSupplierDetailDrawerProps,
        })}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="primary"
              authority="addSupplier"
              onClick={() => {
                setSupplierModalProps({
                  visible: true,
                  recordId: '0',
                  readOnly: false,
                  title: intl.formatMessage({ id: 'purchase.supplier.list.modal.title.add' }),
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.supplier.list.button.add' })}
            </AuthButton>
            <AuthButton
              key="import_supplier"
              authority="importSupplier"
              onClick={() => {
                importData({
                  moduleId: 'BATCH_IMPORT_SUPPLIER',
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: intl.formatMessage({ id: 'purchase.supplier.list.import.taskDesc' }),
                  downloadFileName:
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E4%BE%9B%E5%BA%94%E5%95%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.supplier.list.button.import' })}
            </AuthButton>
            <AuthButton
              authority="exportSupplier"
              onClick={() => {
                exportData({
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: intl.formatMessage({ id: 'purchase.supplier.list.export.taskDesc' }),
                  moduleId: 'SUPPLIER_EXPORT',
                  params: formRef.current?.getFieldsFormatValue?.(),
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.supplier.list.button.export' })}
            </AuthButton>
          </Space>
        }
      />
      <Operation {...supplierModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
      <SupplierDetailDrawer
        {...supplierDetailDrawerProps}
        onClose={() => setSupplierDetailDrawerProps({ open: false })}
      />
    </PageContainer>
  );
};
export default withKeepAlive(SupplierList);
