
import SubTitle from '@/components/common/SubTitle';
import {
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { Drawer, Space, Tag } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useIntl } from 'umi';
import { postStatusOptions } from '../list/types/PostStatus';
import { YesNoStatus } from '../operation/types/YesNo';
import { AddPostEntity, Country } from '../operation/types/add.post.entity';
import { queryFullById } from '../services';

interface SupplierDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  supplierId?: string;
}

export default ({ open, onClose, supplierId }: SupplierDetailDrawerProps) => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });
  const actionRef = useRef<ProDescriptionsActionType>();
  let [record, setRecord] = useState<AddPostEntity>({});

  useEffect(() => {
    if (supplierId) {
      queryFullById({ id: supplierId }).then(res => {
        setRecord(res);
      });
    }
  }, [supplierId]);


  return (
    <Drawer
      title={t('purchase.supplier.detail.title')}
      open={open} onClose={onClose} width={1000}
      bodyStyle={{
        backgroundColor: '#F2F2F2',
      }}
      destroyOnClose
    >
      <ProCard className="rounded-lg">
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Space>
              <span>{record?.supplierInfo?.supplierName}</span>
              <Tag color={postStatusOptions[record?.supplierInfo?.supplierStatus!]?.status}>
                {postStatusOptions[record?.supplierInfo?.supplierStatus!]?.text}
              </Tag>
            </Space>
          }
          dataSource={record.supplierInfo}
          column={5}
          columns={[
            {
              title: intl.formatMessage({ id: 'purchase.supplier.detail.basic.supplierCode' }),
              dataIndex: 'supplierCode',
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.detail.basic.shortName' }),
              dataIndex: 'shortName',
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.country' }),
              dataIndex: 'country',
              valueEnum: Country,
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.abn' }),
              dataIndex: 'abn',
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.clientCode' }),
              dataIndex: 'clientCode',
            },

            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.saleEmail' }),
              dataIndex: 'saleEmail',
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.financeEmail' }),
              dataIndex: 'financeEmail',
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.telephone' }),
              dataIndex: 'telephone',
            },
            {
              title: intl.formatMessage({ id: 'purchase.supplier.operation.basic.remark' }),
              dataIndex: 'remark',
            },
          ]}
        />
      </ProCard>
      <ProCard className="mt-4 rounded-lg" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.detail.contact.title' })} />} wrap>
        {record?.supplierConcatList?.map((s, index) => {
          return (
            <ProDescriptions
              className='border-solid border border-black/[0.08] rounded px-4 pt-4 pb-2 mt-2'
              key={index}
              title={<div className="font-normal text-black/[0.85]">
                <div>
                  {s.firstName} {s.lastName}
                  {s.isDefault == YesNoStatus.NO && <Tag color="blue" className='ml-2'>{intl.formatMessage({ id: 'purchase.supplier.detail.contact.defaultContact' })}</Tag>}
                </div>
              </div>}
              column={4}
              dataSource={s}
              columns={[
                {
                  title: t('purchase.supplier.operation.contact.columns.post'),
                  dataIndex: 'postList',
                  renderText: (value: string[], record) => record?.postList?.map((item) => item.positionName).join(','),
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.detail.contact.contactPhone' }),
                  dataIndex: 'concatPhone',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.detail.contact.email' }),
                  dataIndex: 'email',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.remark' }),
                  dataIndex: 'remark',
                },
              ]}
            />
          );
        })}
      </ProCard>
      <ProCard className="mt-4 rounded-lg" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.detail.address.title' })} />} wrap>
        {record?.supplierAddressList?.map((s, index) => {
          return (
            <ProDescriptions
              key={index}
              className='border-solid border border-black/[0.08] rounded px-4 pt-4 pb-2 mt-2'
              title={<div className="font-normal text-black/[0.85]">
                {intl.formatMessage({ id: 'purchase.supplier.detail.address.addressNumber' }, { number: index + 1 })}
                {s.isDefault == YesNoStatus.NO && (
                  <Tag color="blue" className="ml-2">
                    {intl.formatMessage({ id: 'purchase.supplier.detail.address.defaultAddress' })}
                  </Tag>
                )}
              </div>}
              column={6} dataSource={s}
              columns={[
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.postCode' }),
                  dataIndex: 'postCode',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.suburb' }),
                  dataIndex: 'suburbName',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.state' }),
                  dataIndex: 'stateName',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.detail.address.detailAddress' }),
                  dataIndex: 'detailAddress',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.detail.address.contactPerson' }),
                  dataIndex: 'concatPerson',
                },
                {
                  title: intl.formatMessage({ id: 'purchase.supplier.detail.address.contactPhone' }),
                  dataIndex: 'concatPhone',
                },
              ]}
            />
          );
        })}
      </ProCard>
      <ProCard className="mt-4 rounded-lg" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.detail.settlement.title' })} />} wrap>
        <ProDescriptions column={4} dataSource={record?.supplierSettleInfo}>
          <ProDescriptions.Item dataIndex="settleType" label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.settleType' })} />
          <ProDescriptions.Item
            dataIndex="isMultiCurrency"
            label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.isMultiCurrency' })}
            renderText={(value) => Boolean(value) ? t('common.option.yes') : t('common.option.no')} />
          <ProDescriptions.Item
            dataIndex="gstExcluded"
            label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.gstExcluded' })}
            renderText={(value) => Boolean(value) ? t('common.option.yes') : t('common.option.no')} />
        </ProDescriptions>
        <div className='mb-2 font-semibold'>
          {
            t('purchase.supplier.operation.settlement.columns.accountName')
          }
        </div>
        {
          record.supplierSettleAccountList?.map((item, index) => {
            return <ProDescriptions
              className='border-solid border border-black/[0.08] rounded px-4 pt-4 pb-2 mt-2'
              key={index}
              column={4}
              dataSource={item}
              columns={[
                {
                  title: t('purchase.supplier.operation.settlement.columns.bsb'),
                  dataIndex: 'bsb',
                },
                {
                  title: t('purchase.supplier.operation.settlement.columns.swiftCode'),
                  dataIndex: 'swiftCode',
                },
                {
                  title: t('purchase.supplier.operation.settlement.columns.accountNumber'),
                  dataIndex: 'accountNumber',
                },
                {
                  title: t('purchase.supplier.operation.settlement.columns.accountName'),
                  dataIndex: 'accountName',
                },
              ]}
            />
          })
        }
      </ProCard>
    </Drawer>
  );
};
