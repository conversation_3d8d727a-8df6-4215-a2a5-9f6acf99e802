import { PublicHoliday } from '@/pages/home/<USER>/Calendar/config';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import {
  ApproveLeaveApplyRequest,
  LeaveApplyEntity,
  LeaveEntity,
  PayLeaveEntity,
} from './types/leave.entity';
import { LeaveType } from './types/leave.enum';
import { LeaveCalendar } from './types/leaveCalendar.entity';
import { AccountLeaveAggregation, RemainLeaveEntity } from './types/remainLeave.entity';

/**
 * 新增请假申请
 */
export async function insertLeaveApply(params: Partial<LeaveApplyEntity>): Promise<boolean> {
  return request('/ipmspassport/LeaveApplyFacade/insert', {
    data: params,
  });
}

/**
 * 更新请假申请
 */
export async function updateLeaveApply(
  params: Partial<LeaveApplyEntity & { id: string }>,
): Promise<boolean> {
  return request('/ipmspassport/LeaveApplyFacade/update', {
    data: params,
  });
}

/**
 * 新增请假申请
 */
export async function adjustLeave(params: Partial<PayLeaveEntity>): Promise<boolean> {
  return request('/ipmspassport/LeaveApplyFacade/adjustLeave', {
    data: params,
  });
}

/**
 * 通过请假申请
 */
export const passLeaveApply = (params: ApproveLeaveApplyRequest): Promise<boolean> => {
  return request('/ipmspassport/LeaveApplyFacade/pass', {
    data: params,
  });
};

/**
 * 拒绝请假申请
 */
export const rejectLeaveApply = (params: ApproveLeaveApplyRequest): Promise<boolean> => {
  return request('/ipmspassport/LeaveApplyFacade/reject', {
    data: params,
  });
};

/**
 * 取消/作废假期申请
 */
export const cancelLeaveApply = (params: { id?: string }): Promise<boolean> => {
  return request('/ipmspassport/LeaveApplyFacade/cancel', {
    data: params,
  });
};

/**
 * 根据时间范围计算净工作时间
 */
export const calculateWorkingHours = (params: {
  accountId: string;
  startTime: string;
  endTime: string;
}): Promise<{
  effectiveWorkHours: number; //有效工作时间（小时，保留1位小数）
}> => {
  return request('/ipmspassport/LeaveApplyFacade/calculateWorkingHours', {
    data: params,
  });
};

export const queryLeaveList = (params: Partial<LeaveEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<LeaveEntity>>(
    `/ipmspassport/LeaveQueryFacade/pageQueryApplyList`,
    {
      data: params,
    },
  );
};

/**
 * 查询指定员工假期余额
 */
export const queryRemainingLeave = (params: Partial<LeaveEntity>): Promise<RemainLeaveEntity> => {
  return request(`/ipmspassport/LeaveQueryFacade/queryAggregationSummary`, {
    data: params,
  });
};

/**
 * 分页查询员工假期余额
 */
export const queryqueryRemainingLeaveList = (
  params: PageRequestParamsType & { accountIdList?: string[] },
): Promise<PageResponseDataType<RemainLeaveEntity>> => {
  return request(`/ipmspassport/LeaveQueryFacade/pageQueryAggregationSummaryList`, {
    data: params,
  });
};

/**
 * 更新备注
 */
export const updateComments = (params: { id: string; comments: string }): Promise<boolean> => {
  return request('/ipmspassport/LeaveApplyFacade/updateComments', {
    data: params,
  });
};

/**
 * 首页-查询假期日历
 */
export const queryLeaveCalendar = (params: {
  startTime: string;
  endTime: string;
  storeIdList?: string[];
}): Promise<LeaveCalendar> => {
  return request(`/ipmspassport/LeaveQueryFacade/queryLeaveCalendar`, {
    data: params,
  }).then((res) => {
    const { accountLeaveApplyList = [], publicHolidayList = [] } = res ?? {};
    return accountLeaveApplyList.map((item) => {
      const startDate = new Date(item.startTime);
      const endDate = new Date(item.endTime);
      const isSameDay = startDate.getDate() === endDate.getDate() &&
        startDate.getMonth() === endDate.getMonth() &&
        startDate.getFullYear() === endDate.getFullYear();

      const formatTime = (date: Date) => {
        if (isSameDay) {
          return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
      };

      return {
        ...item,
        sourceId: item.id,
        title: item.accountName,
        startTimeStr: (startDate),
        endTimeStr: (endDate),
        start: startDate,
        end: endDate,
      };
    }).concat(publicHolidayList.map((item: { name: string; startTime: string; endTime: string; }) => {
      return {
        ...item,
        type: PublicHoliday,
        title: item.name,
        start: new Date(item.startTime),
        end: new Date(item.endTime),
      };
    }));
  });
};


/**
 * 查询假期记录
 */
export const queryLeaveAggregationList = (params: {
  accountId: string;
  type: LeaveType;
}): Promise<AccountLeaveAggregation[]> => {
  return request('/ipmspassport/LeaveQueryFacade/queryLeaveAggregationList', {
    data: params,
  });
};


/**
 * 审核页面剩余时长
 */
export const getLeaveAggregation = (params: {
  accountId: string;
  type: LeaveType;
}): Promise<{ currentTotalHours: number }> => {
  return request('/ipmspassport/LeaveQueryFacade/getLeaveAggregation', {
    data: params,
  });
};