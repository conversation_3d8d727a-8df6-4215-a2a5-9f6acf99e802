import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { compressImage } from '@/utils/fileUtils';
import {
  ModalForm,
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormSelect,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { Flex, message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import {
  calculateWorkingHours,
  insertLeaveApply,
  queryRemainingLeave,
  updateLeaveApply,
} from '../../services';
import { LeaveApplyEntity, LeaveEntity } from '../../types/leave.entity';
import { LeaveExchangeChannel, LeaveType, LeaveTypeOptions } from '../../types/leave.enum';

interface ApplyLeaveModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  leave?: LeaveEntity;
}

const ApplyLeaveModal: React.FC<ApplyLeaveModalProps> = ({
  visible,
  onClose,
  onSuccess,
  leave,
}) => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });
  const { initialState } = useModel('@@initialState');

  const [leaveHours, setLeaveHours] = useState<number>(0);
  const [remainHours, setRemainHours] = useState<number>(0);

  const [form] = ProForm.useForm();
  const leaveTime = ProForm.useWatch('leaveTime', form);
  const type = ProForm.useWatch('type', form);
  const accountId = ProForm.useWatch('accountId', form);

  const showRemainHours = useMemo(() => {
    return [LeaveType.Sick_Leave, LeaveType.Annual_Leave, LeaveType.Long_Service_Leave].includes(
      Number(type),
    );
  }, [type]);

  const handleSubmit = async (values: Partial<LeaveApplyEntity>) => {
    if (leaveHours === 0) {
      message.warning(t('personnel.leave.message.leaveHoursRequired'));
      return false;
    }
    const params = {
      ...values,
      exchangeChannel: LeaveExchangeChannel.Leave,
      images: values.images?.map((item) => ({ url: item.response?.data?.[0], name: item.name })),
    };
    let result = false;
    if (leave) {
      result = await updateLeaveApply({ ...params, id: leave.id });
    } else {
      result = await insertLeaveApply({ ...params });
    }
    if (result) {
      onSuccess();
      onClose();
    }
  };

  useEffect(() => {
    const [startTime, endTime] = leaveTime || [];
    if (startTime && endTime && accountId) {
      calculateWorkingHours({
        accountId,
        startTime: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
      }).then((res) => {
        setLeaveHours(res?.effectiveWorkHours ?? 0);
      });
    }
  }, [leaveTime, accountId]);

  useEffect(() => {
    if (showRemainHours && accountId) {
      queryRemainingLeave({ accountId }).then((res) => {
        if (type == LeaveType.Sick_Leave) {
          setRemainHours(res?.sickBalanceHours ?? 0);
        }
        if (type == LeaveType.Annual_Leave) {
          setRemainHours(res?.yearBalanceHours ?? 0);
        }
        if (type == LeaveType.Long_Service_Leave) {
          setRemainHours(res?.serviceBalanceHours ?? 0);
        }
      });
    }
  }, [type, showRemainHours, accountId]);

  useEffect(() => {
    if (leave) {
      form.setFieldsValue({
        ...leave,
        leaveTime: [dayjs(leave.startTime), dayjs(leave.endTime)],
      });
    } else {
      form.resetFields();
    }
  }, [leave]);

  return (
    <ModalForm<LeaveApplyEntity>
      title={t('personnel.leave.applyLeave')}
      open={visible}
      width={600}
      form={form}
      onFinish={handleSubmit}
      onOpenChange={(oepn) => {
        form.resetFields();
        if (!oepn) {
          onClose();
        }
      }}
    >
      <ProFormSelect
        name="accountId"
        label={t('personnel.leave.applyPerson')}
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={() => accountListQuerySimple({})}
        rules={[{ required: true }]}
        initialValue={initialState?.currentUser?.accountId}
      />

      <ProFormSelect
        name="type"
        label={t('personnel.leave.leaveType')}
        rules={[{ required: true }]}
        valueEnum={LeaveTypeOptions}
        formItemProps={{
          help: showRemainHours && (
            <>
              {t('personnel.leave.help.leaveRemainHours')}{' '}
              <span className="text-primary">{remainHours}</span> {t('personnel.leave.unit.hour')}
            </>
          ),
        }}
      />
      <ProFormDateRangePicker
        name="leaveTime"
        label={t('personnel.leave.leaveTime')}
        rules={[{ required: true }]}
        fieldProps={{
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
        }}
        transform={(value) => {
          return {
            startTime: value[0] ? dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss') : undefined,
            endTime: value[1] ? dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss') : undefined,
          };
        }}
        formItemProps={{
          help: (
            <>
              {t('personnel.leave.help.leaveHours')}{' '}
              <span className="text-primary">{leaveHours}</span> {t('personnel.leave.unit.hour')}
            </>
          ),
        }}
      />
      <ProFormTextArea name="reason" label={t('personnel.leave.reason')} />
      <ProFormDependency name={['type']}>
        {({ type }) => {
          if (type == LeaveType.Sick_Leave) {
            return (
              <ProFormUploadButton
                name="images"
                label={t('personnel.leave.doctorCertificate')}
                action="/apigateway/public/upload/object/batch"
                max={1}
                fieldProps={{
                  listType: 'picture-card',
                  beforeUpload: (file) => compressImage(file),
                }}
                rules={[{ required: true }]}
              />
            );
          }
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default ApplyLeaveModal;
