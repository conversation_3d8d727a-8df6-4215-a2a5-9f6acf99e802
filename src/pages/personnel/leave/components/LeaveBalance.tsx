import FunProTable from '@/components/common/FunProTable';
import { InfoCircleOutlined, SwapOutlined } from '@ant-design/icons';
import { ProTable, type ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Modal, Tag } from 'antd';
import { useState } from 'react';
import { accountListQuerySimple, queryStoreByAccount } from '../../user/services';
import { queryLeaveAggregationList, queryqueryRemainingLeaveList } from '../services';
import { LeaveType } from '../types/leave.enum';
import type { AccountLeaveAggregation, RemainLeaveEntity } from '../types/remainLeave.entity';

interface BalanceLeaveProps {
  unit: 'hour' | 'day' | 'week';
  value?: number;
  className?: string;
  onClick?: () => void;
}
const BalanceLeave = (props: BalanceLeaveProps) => {
  const { unit, value, className, onClick } = props;
  if (value === undefined || value === null) return '-'
  if (unit === 'hour') {
    return <span className={className} onClick={onClick}>{value}h</span>;
  }
  if (unit === 'day') {
    return <span className={className} onClick={onClick}>≈{value}d</span>;
  }
  if (unit === 'week') {
    return <span className={className} onClick={onClick}>≈{value}wk</span>;
  }
  return '-';
}

const LeaveBalance = () => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });
  // 1: 小时 2: 天/周
  const [unitType, setUnitType] = useState<1 | 2>(1);
  const isHour = unitType === 1;
  // modal state
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [detailList, setDetailList] = useState<any[]>([]);

  const columns: ProColumns<RemainLeaveEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: t('personnel.leave.employee'),
      dataIndex: 'accountName',
      key: 'memberName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'accountId',
      },
    },
    {
      title: t('personnel.leave.column.store'),
      dataIndex: 'storeNames',
      width: 180,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: t('personnel.leave.type.annualLeave'),
      search: false,
      children: [
        {
          title: t('personnel.leave.balance.total'), dataIndex: isHour ? 'yearTotalHours' : 'yearTotalHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
        {
          title: t('personnel.leave.balance.used'), dataIndex: isHour ? 'yearUsedHours' : 'yearUsedHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
        {
          title: t('personnel.leave.balance.adjust'), dataIndex: isHour ? 'yearAdjustTotalHours' : 'yearAdjustTotalHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
        {
          title: t('personnel.leave.balance.remaining'), dataIndex: isHour ? 'yearBalanceHours' : 'yearBalanceHoursConvertDay', search: false,
          render: (text, record) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'}
            className='text-[#0099d3] cursor-pointer'
            onClick={async () => {
              if (!record?.accountId) return;
              setDetailVisible(true);
              setDetailLoading(true);
              try {
                const res = await queryLeaveAggregationList({ accountId: String(record.accountId), type: LeaveType.Annual_Leave });
                setDetailList(res || []);
              } finally {
                setDetailLoading(false);
              }
            }}
          />
        },
      ],
    },
    {
      title: t('personnel.leave.type.sickLeave'),
      search: false,
      children: [
        {
          title: t('personnel.leave.balance.total'), dataIndex: isHour ? 'sickTotalHours' : 'sickTotalHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
        {
          title: t('personnel.leave.balance.used'), dataIndex: isHour ? 'sickUsedHours' : 'sickUsedHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
        {
          title: t('personnel.leave.balance.remaining'), dataIndex: isHour ? 'sickBalanceHours' : 'sickBalanceHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
      ]
    },
    {
      title: t('personnel.leave.type.longServiceLeave'),
      search: false,
      children: [
        {
          title: t('personnel.leave.balance.total'), dataIndex: isHour ? 'serviceTotalHours' : 'serviceTotalHoursConvertWeek', search: false,
          render: (text, record) => <>
            <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'week'} />
            {
              record.serviceLeaveLessThan7Years && <Tag color='red' className='ml-1'>{t('personnel.leave.balance.serviceLeaveLessThan7Years')}</Tag>
            }
          </>
        },
        {
          title: t('personnel.leave.balance.used'), dataIndex: isHour ? 'serviceUsedHours' : 'serviceUsedHoursConvertWeek', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'week'} />
        },
        {
          title: t('personnel.leave.balance.adjust'), dataIndex: isHour ? 'serviceAdjustTotalHours' : 'serviceAdjustTotalHoursConvertDay', search: false,
          render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
        },
        {
          title: t('personnel.leave.balance.remaining'), dataIndex: isHour ? 'serviceBalanceHours' : 'serviceBalanceHoursConvertWeek', search: false,
          render: (text,) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'week'} />
        },
      ]
    },
  ];

  return (
    <>
      <FunProTable<RemainLeaveEntity, any>
        columns={columns}
        requestPage={queryqueryRemainingLeaveList}
        scroll={{ x: 'max-content' }}
        bordered
        toolbar={{
          settings: [<div key="unit-toggle" className='text-sm text-gray-500 cursor-default' >
            <InfoCircleOutlined />
            <span className='mx-1'>{t('personnel.leave.balance.unitConversion')}</span>
            <SwapOutlined className='cursor-pointer' onClick={() => setUnitType(unitType === 1 ? 2 : 1)} />
          </div>]
        }}
      />
      <Modal
        title={t('personnel.leave.balance.detail')}
        open={detailVisible}
        footer={null}
        onCancel={() => setDetailVisible(false)}
        confirmLoading={detailLoading}
      >
        <ProTable<AccountLeaveAggregation>
          search={false}
          pagination={false}
          dataSource={detailList}
          toolBarRender={false}
          columns={[
            {
              title: t('personnel.leave.balance.year'),
              dataIndex: 'year'
            },
            {
              title: t('personnel.leave.balance.currentTotalHours'),
              dataIndex: isHour ? 'currentTotalHours' : 'currentTotalHoursConvertDay',
              render: (text) => <BalanceLeave value={Number(text) || 0} unit={isHour ? 'hour' : 'day'} />
            }
          ]}
        />
      </Modal>
    </>
  );
};

export default LeaveBalance;
