/**
 * 销售单打印模板
 * @constructor
 */
import logo from '@/assets/imgs/logo.svg';
import { PrintConfig } from '@/pages/common/print';
import { getOrderByOrderNoForDbReturnSelected } from '@/pages/sales/order/edit/services';
import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { Spin } from 'antd';
import JsBarcode from 'jsbarcode';
import { useEffect, useState } from 'react';

const borderStyle = {
  border: '1px solid #d9d9d9',
}

export interface SalesPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.salesOrder];
}

const Sales = (props: SalesPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();

  console.log('currentPrintConfig', currentPrintConfig);

  const searchParams = new URLSearchParams(window.location.search);
  const orderNo = searchParams.get('orderNo');

  useEffect(() => {
    if (orderNo) {
      setLoading(true);
      getOrderByOrderNoForDbReturnSelected(orderNo)
        .then((result) => {
          if (result) {
            setOrderDetail(result);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [orderNo]);

  useEffect(() => {
    if (orderDetail?.orders?.storeId) {
      // queryPostDetail({ id: orderDetail?.orders?.storeId }).then((result) => {
      //   if (result) {
      //     setStoreDetail(result);
      //   }
      // });
      JsBarcode("#barcode", orderDetail?.orders?.orderNo ?? '', {
        height: 40,
        fontSize: 14
      })
    }
  }, [orderDetail]);


  // 格式化日期
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleDateString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (!orderDetail) return null;

  // 计算商品总计
  const calculateItemTotal = () => {
    return orderDetail?.orderGoodsROList?.reduce((total, item) => {
      return total + (item.actualSellingTotalAmountYuan || 0);
    }, 0) || 0;
  };

  // 计算GST
  const calculateGST = () => {
    const itemTotal = calculateItemTotal();
    return itemTotal * 0.1; // 10% GST
  };

  // 计算总计
  const calculateGrandTotal = () => {
    const itemTotal = calculateItemTotal();
    const gst = calculateGST();
    return itemTotal + gst;
  };

  const totalDiscount = ((orderDetail?.orderGoodsROList ?? [])?.reduce((total, item) => {
    return total + (item.discountSum || 0);
  }, 0) / 100).toFixed(2);

  const itemTotal = (orderDetail?.orderGoodsROList?.reduce((total, item) => {
    return total + (item.actualSellingTotalAmountYuan || 0);
  }, 0) || 0).toFixed(2);

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <div className="print-preview-container">
          <div className="invoice-container" style={{
            margin: '0 auto',
            fontFamily: 'Arial, sans-serif',
            fontSize: '12px',
            lineHeight: '1.4'
          }}>
            {/* Header Section */}
            <div style={{ marginBottom: '20px' }} className='flex justify-between'>
              {/* Logo and Company Name */}
              <div style={{
                width: 160
              }}>
                <img src={logo} alt="logo" style={{ width: '100%', height: 'auto' }} />
              </div>

              {/* Invoice Title */}
              <div style={{ textAlign: 'center', }}>
                <h1 style={{
                  fontSize: '32px',
                  margin: '0',
                  color: '#333'
                }}>
                  GripX Auto Parts {orderDetail?.orders?.saleCompanyName}
                </h1>
                <h2 style={{
                  fontSize: '24px',
                  margin: '0',
                  color: '#333'
                }}>
                  TAX INVOICE
                </h2>
              </div>

              <div style={{ textAlign: 'right' }}>
                <img id="barcode" />
              </div>

            </div>

            {/* Customer and Invoice Info */}
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              {/* Customer Info */}
              <div style={{ flex: 1 }}>
                <div><strong>Customer Code:</strong> {orderDetail?.orders?.cstId}</div>
                <div><strong>Customer Name:</strong> {orderDetail?.orders?.cstName}</div>
                <div><strong>Address:</strong> {orderDetail?.orderFixedAddressList?.[0]?.consigneeDetail}</div>
                <div><strong>Tel:</strong> {orderDetail?.orders?.cstPhone}</div>
                <div><strong>Email:</strong></div>
              </div>

              {/* Invoice Info */}
              <div style={{ flex: 1, textAlign: 'right' }}>
                {/* TODO: 销售主体下门店的联系方式 */}
                <div><strong>ABN:</strong> </div>
                <div><strong>Date:</strong> {formatDate(orderDetail?.orders?.orderCreateTime)}</div>
                <div><strong>Reference:</strong> {orderDetail?.orders?.referenceNo}</div>
                <div><strong>Invoice #:</strong> {orderDetail?.orders?.orderNo}</div>
              </div>
            </div>



            {/* Order Total */}
            <div style={{ textAlign: 'right', marginBottom: '10px', fontSize: '14px' }}>
              <strong>Order Total: {calculateGrandTotal().toFixed(2)}</strong>
            </div>

            {/* Products Table */}
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              marginBottom: '20px',
              ...borderStyle
            }}>
              <thead>
                <tr style={{ backgroundColor: '#f0f0f0' }}>
                  <th style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>Quantity</th>
                  <th style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>Product Code</th>
                  <th style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>Description</th>
                  <th style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>Unit Price</th>
                  <th style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>Discount</th>
                  <th style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>Subtotal</th>
                </tr>
              </thead>
              <tbody>
                {orderDetail?.orderGoodsROList?.map((item) => (
                  <tr key={item.id}>
                    <td style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>
                      {item.saleNum}
                    </td>
                    <td style={{ ...borderStyle, padding: '4px', textAlign: 'center' }}>
                      {item.itemSn}
                    </td>
                    <td style={{ ...borderStyle, padding: '4px' }}>
                      {item.itemName}
                    </td>
                    <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                      {(item.unitPriceYuan || 0).toFixed(2)}
                    </td>
                    <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                      {((item.discountSum || 0) / 100).toFixed(2)}
                    </td>
                    <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                      {(item.actualSellingTotalAmountYuan || 0).toFixed(2)}
                    </td>
                  </tr>
                ))}

                {/* Summary rows within table */}
                <tr>
                  <td colSpan={4} style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontWeight: 'bold' }}>
                    Item total
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                    {totalDiscount}
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                    {itemTotal}
                  </td>
                </tr>

                <tr>
                  <td colSpan={4} style={{ ...borderStyle, padding: '4px', fontSize: '11px' }}>
                    <div><strong>Payment Terms:</strong> COD &nbsp;&nbsp;&nbsp; <strong>Account Balance:</strong> -38.51</div>
                    <div style={{ fontSize: '10px', marginTop: '2px' }}>
                      *This balance might be varied if you have made payments in the last two business days
                    </div>
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontWeight: 'bold' }}>
                    Order Discount
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                    0.00
                  </td>
                </tr>

                <tr>
                  <td colSpan={4} style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontSize: '11px' }}>
                    Shipping: 0.00 <strong>Sub Total:{calculateItemTotal().toFixed(2)} GST: {calculateGST().toFixed(2)}</strong>
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontWeight: 'bold' }}>
                    Grand Total
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                    {calculateGrandTotal().toFixed(2)}
                  </td>
                </tr>

                <tr>
                  <td colSpan={4}>
                    Notes:
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontWeight: 'bold' }}>
                    Paid
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right' }}>
                    {calculateGrandTotal().toFixed(2)}
                  </td>
                </tr>

                <tr>
                  <td colSpan={4}>
                    {orderDetail?.orders?.remark}
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontWeight: 'bold' }}>
                    Balance
                  </td>
                  <td style={{ ...borderStyle, padding: '4px', textAlign: 'right', fontWeight: 'bold' }}>
                    0.10
                  </td>
                </tr>
              </tbody>
            </table>

            {/* Bank Details */}
            <div style={{ marginBottom: '20px' }}>
              <div><strong>Please pay to our account:</strong> COMMONWEALTH BANK</div>
              <div>BSB & Ac No: 063385 - 1076 6776</div>
              <div><strong>Or mail your cheque to address:</strong> GripX Auto Parts Sunshine</div>
              <div>P.O. BOX 476, BOX HILL VIC 3128</div>
            </div>

            {/* Terms and Conditions */}
            <div style={{ fontSize: '10px', lineHeight: '1.3' }}>
              <div>*WTY = Warranty related orders</div>
              <div>Terms</div>
              <div>
                <p>
                  1. It is to GripX Auto Parts absolute discretion of whether providing replacement of the returned goods or a Note of Credit. 5% of the price restocking fee may apply. All goods shall remain the absolute property of GripX
                  Auto Parts until payment to invoice is received in full.
                </p>
                <p>
                  2.  Goods for return must be unused and packed in original condition. All returns must be made within fourteen days from the purchase and the proof of purchase must be provided.
                </p>
                <p>
                  2.  Any claim for manufacturer warranty will be dealt with according to the relevant manufacturer&apos;s warranty policy, terms and conditions of which will be varied and by any case will not extend to any labour costs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default Sales;
