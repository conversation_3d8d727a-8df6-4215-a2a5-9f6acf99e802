import { queryBillDetailPage } from "@/pages/finance/bill/services";
import { useEffect, useState } from "react";
// import logo from '@/assets/imgs/logo.svg';


const Bill = () => {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    queryBillDetailPage({ id }).then((res) => {
      console.log(res);
    });
  }, []);


  return (
    <div>

    </div>
  );
};

export default Bill;