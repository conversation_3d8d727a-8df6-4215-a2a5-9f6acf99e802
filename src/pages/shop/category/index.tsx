import { PageContainer } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber, message, Popconfirm, Space } from 'antd';
import AuthButton from '@/components/common/AuthButton';
import {
  categoryBatchUpdate,
  categoryDelete,
  queryAppExtCategoryTree,
} from '@/pages/shop/category/services';
import { CategoryEntity } from '@/pages/shop/category/types/category.entity';
import AddModal, { AddModalProps } from '@/pages/shop/category/components/AddModal';
import { useIntl } from 'react-intl';
import _ from 'lodash';
import FunProTable from '@/components/common/FunProTable';
import { categoryStatusOptions } from '@/pages/shop/category/types/category.status';

export default function Category() {
  const [dataSource, setDataSource] = useState<CategoryEntity[]>([]);
  const intl = useIntl();
  const [expandedRowKeys, setExpandedRowKeys] = useState<readonly React.Key[]>([]);
  const [addModalProps, setAddModalProps] = useState<AddModalProps>({
    visible: false,
    isEdit: false,
    currentCategoryItem: undefined,
    parentCategoryItem: undefined,
  });

  const queryList = () => {
    queryAppExtCategoryTree().then((data) => {
      // @ts-ignore
      setDataSource(data);
    });
  };

  useEffect(() => {
    queryList();
  }, []);

  const updateTreeNode = (
    nodes: any[],
    id: string | number,
    updater: (node: any) => void,
  ): any[] => {
    return nodes.map((node) => {
      if (node.id === id) {
        updater(node);
      } else if (node.children) {
        node.children = updateTreeNode(node.children, id, updater);
      }
      return node;
    });
  };

  const findParentItem = (tree: CategoryEntity[], childId: string) => {
    let result = undefined;

    const walk = (nodes: CategoryEntity[]) => {
      for (let node of nodes) {
        if (node.children?.some((child) => child.id === childId)) {
          result = node;
          return;
        }
        if (node.children) {
          walk(node.children);
        }
      }
    };

    walk(tree);
    return result;
  };

  function flattenTree(tree: any[]): any[] {
    const result: any[] = [];

    function traverse(nodes: any[]) {
      nodes.forEach((node) => {
        const { children, ...rest } = node;
        result.push(rest);
        if (children && children.length) {
          traverse(children);
        }
      });
    }

    traverse(tree);

    return result;
  }

  const columns: ProColumns[] = [
    {
      title: intl.formatMessage({ id: 'shop.category.categoryName' }),
      dataIndex: 'extCategoryName',
      width: 200,
    },
    {
      title: intl.formatMessage({ id: 'shop.category.jumpType' }),
      dataIndex: 'jumpType',
      width: 100,
      render: (text) => {
        if (text === 1) {
          return intl.formatMessage({ id: 'shop.category.keyword' });
        } else {
          return '-';
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'shop.category.jumpContent' }),
      dataIndex: 'keyWord',
      width: 200,
    },
    {
      title: intl.formatMessage({ id: 'shop.category.status' }),
      dataIndex: 'isShow',
      width: 100,
      valueEnum: categoryStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'shop.category.sort' }),
      dataIndex: 'extCategoryOrder',
      width: 100,
      render: (_text, record) => (
        <InputNumber
          style={{ width: '80px' }}
          value={record.extCategoryOrder}
          min={1}
          onChange={(v) => {
            const newData = _.cloneDeep(dataSource);
            const newValue = v ? parseInt(v, 10) : undefined;
            const updated = updateTreeNode(newData, record.id, (node) => {
              node.extCategoryOrder = newValue;
            });
            setDataSource(updated);
          }}
        />
      ),
    },
  ];

  const operatorColumn: ProColumns<CategoryEntity> = {
    title: intl.formatMessage({ id: 'common.column.operation' }),
    valueType: 'option',
    width: 150,
    render: (_text, record: CategoryEntity) => {
      return (
        <Space>
          {record.categoryLevel < 3 && (
            <AuthButton
              isHref
              authority=""
              onClick={() => {
                setAddModalProps({
                  visible: true,
                  isEdit: false,
                  currentCategoryItem: undefined,
                  parentCategoryItem: record,
                });
              }}
            >
              {intl.formatMessage({ id: 'shop.category.add.child' })}
            </AuthButton>
          )}
          <AuthButton
            isHref
            authority=""
            onClick={() => {
              setAddModalProps({
                visible: true,
                isEdit: true,
                currentCategoryItem: record,
                parentCategoryItem: findParentItem(dataSource, record.id),
              });
            }}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          <Popconfirm
            title={intl.formatMessage({ id: 'common.confirm.delete' })}
            onConfirm={() => {
              categoryDelete(record.id).then((res) => {
                if (res) {
                  message.success(intl.formatMessage({ id: 'common.message.deleteSuccess' }));
                  queryList();
                }
              });
            }}
          >
            <AuthButton isHref authority="">
              {intl.formatMessage({ id: 'common.button.delete' })}
            </AuthButton>
          </Popconfirm>
        </Space>
      );
    },
  };

  return (
    <PageContainer>
      <FunProTable
        scroll={{ x: 'max-content' }}
        headerTitle={
          <Space>
            <AuthButton
              authority=""
              type={'primary'}
              onClick={() => {
                setAddModalProps({
                  visible: true,
                  isEdit: false,
                  currentCategoryItem: undefined,
                  parentCategoryItem: undefined,
                });
              }}
            >
              {intl.formatMessage({ id: 'shop.category.add' })}
            </AuthButton>
            <AuthButton
              authority=""
              onClick={() => {
                categoryBatchUpdate(flattenTree(dataSource)).then((res) => {
                  if (res) {
                    message.success(intl.formatMessage({ id: 'common.message.save.success' }));
                    queryList();
                  }
                });
              }}
            >
              {intl.formatMessage({ id: 'shop.category.updateSort' })}
            </AuthButton>
            <Button
              onClick={() => {
                const keys: string[] = [];
                const walk = (nodes: CategoryEntity[]) => {
                  nodes.forEach((node) => {
                    if (node.children?.length) {
                      keys.push(node.id);
                      walk(node.children);
                    }
                  });
                };
                walk(dataSource);
                setExpandedRowKeys(keys);
              }}
            >
              {intl.formatMessage({ id: 'shop.category.expandAll' })}
            </Button>
            <Button onClick={() => setExpandedRowKeys([])}>
              {intl.formatMessage({ id: 'shop.category.collapseAll' })}
            </Button>
          </Space>
        }
        columns={[...columns, operatorColumn]}
        rowKey="id"
        search={false}
        pagination={false}
        dataSource={dataSource}
        rowClassName={(record, index) => {
          if (record.categoryLevel === 3) {
            return 'bg-gray-50';
          }
          return '';
        }}
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: setExpandedRowKeys,
        }}
      />
      <AddModal
        {...addModalProps}
        onClose={() =>
          setAddModalProps({
            visible: false,
            isEdit: false,
            currentCategoryItem: undefined,
            parentCategoryItem: undefined,
          })
        }
        onSuccess={() => {
          queryList();
        }}
      />
    </PageContainer>
  );
}
