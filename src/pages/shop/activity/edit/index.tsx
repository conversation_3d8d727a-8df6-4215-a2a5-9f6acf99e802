import AuthButton from '@/components/common/AuthButton';
import { useKeepAliveTabs } from '@/layouts/useKeepAliveTabs';
import { PageContainer } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { ActivityType } from '../../types/ActivityEnum';
import BuyGiftActivtyForm from './activity/BuyGift';
import FullGift from './activity/FullGift';
import SpecialActivityForm from './activity/SpecialPrice';
import SuiteItem from './activity/SuiteItem';
import { addActivity, editActivity } from './service';

export interface ActivityFormProps {
  onSubmit: (params: any) => Promise<any>;
}

export const SubmitArea: React.FC<{
  readonly: boolean;
  submit: () => void;
  reset: () => void;
}> = (props) => {
  const { readonly } = props;
  const intl = useIntl();
  const { closeTab } = useKeepAliveTabs();

  const onCancel = () => {
    props.reset?.();
    history.push('/shop/activity/list'); //返回列表页
    closeTab?.();
  };

  return (
    <div className="bg-white p-4 mt-4 flex justify-end rounded-lg">
      <Space>
        {readonly ? (
          <Button onClick={() => history.back()}>
            {intl.formatMessage({ id: 'common.button.close' })}
          </Button>
        ) : (
          <>
            <Button onClick={() => onCancel()}>
              {intl.formatMessage({ id: 'common.button.cancel' })}
            </Button>
            <AuthButton authority="saveActivity" type="primary" onClick={() => props.submit?.()}>
              {intl.formatMessage({ id: 'common.button.save' })}
            </AuthButton>
          </>
        )}
      </Space>
    </div>
  );
};

const ActivityForm: React.FC = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const activityType = Number(searchParams.get('activityType'));
  const activityId = searchParams.get('id');
  const type = searchParams.get('type'); // view | edit | copy
  const intl = useIntl();
  const { closeTab } = useKeepAliveTabs();

  const onSubmit = async (params) => {
    return new Promise((resolve) => {
      if (activityId && type === 'edit') {
        editActivity(params).then((result) => {
          resolve(result);
        });
      } else {
        addActivity(params).then((result) => {
          resolve(result);
        });
      }
    }).then((result) => {
      if (result) {
        message.success(intl.formatMessage({ id: 'common.message.submitSuccess' }));
        closeTab?.();
        history.push('/shop/activity/list');
      }
    });
  };

  return (
    <PageContainer>
      {activityType == ActivityType.SPECIAL_PRICE && <SpecialActivityForm onSubmit={onSubmit} />}
      {activityType == ActivityType.BUY_GIFT_SELF && <BuyGiftActivtyForm onSubmit={onSubmit} />}
      {[ActivityType.LADDER_FULL_GIFT, ActivityType.EVERY_FULL_GIFT].includes(activityType) && (
        <FullGift onSubmit={onSubmit} />
      )}
      {activityType == ActivityType.SUITE_ITEM && <SuiteItem onSubmit={onSubmit} />}
    </PageContainer>
  );
};

export default ActivityForm;
