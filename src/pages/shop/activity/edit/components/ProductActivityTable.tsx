import EditableDragTable, {
  EditableDragTableProps,
  EditableDragTableRef,
} from '@/components/EditableDragTable';
import SelectProductDrawer from '@/pages/shop/topic/decoration/CraftEditor/components/LinkSettings/SelectProductDrawer';
import { ActivityItemEntity } from '@/pages/shop/types/ActivityDataType';
import { ActivityType } from '@/pages/shop/types/ActivityEnum';
import { importData } from '@/utils/importData';
import { useIntl } from '@umijs/max';
import { Button, Space, message } from 'antd';
import { uniqBy } from 'lodash';
import React, { useMemo, useState } from 'react';
import { queryActivityItemByTaskId } from '../service';

const activityItemImport = {
  [ActivityType.SPECIAL_PRICE]: {
    moduleId: 'SPECIAL_PRICE_ACTIVITY_ITEM_IMPORT',
    taskDesc: 'shop.activity.products.taskDesc.specialPrice',
    downloadFileName:
      'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%BA%93%E4%BD%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
  },
  [ActivityType.BUY_GIFT_SELF]: {
    moduleId: 'BUY_GIFT_SELF_ACTIVITY_ITEM_IMPORT',
    taskDesc: 'shop.activity.products.taskDesc.buyGiftSelf',
    downloadFileName:
      'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E9%87%87%E8%B4%AD%E6%98%8E%E7%BB%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
  },
  [ActivityType.EVERY_FULL_GIFT]: {
    moduleId: 'FULL_GIFT_ACTIVITY_ITEM_IMPORT',
    taskDesc: 'shop.activity.products.taskDesc.everyFullGift',
    downloadFileName:
      'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%BA%93%E4%BD%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
  },
  [ActivityType.LADDER_FULL_GIFT]: {
    moduleId: 'FULL_GIFT_ACTIVITY_ITEM_IMPORT',
    taskDesc: 'shop.activity.products.taskDesc.ladderFullGift',
    downloadFileName:
      'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%BA%93%E4%BD%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
  },
  [ActivityType.SUITE_ITEM]: {
    moduleId: 'SUITE_ITEM_ACTIVITY_ITEM_IMPORT',
    taskDesc: 'shop.activity.products.taskDesc.suiteItem',
    downloadFileName:
      'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E9%87%87%E8%B4%AD%E6%98%8E%E7%BB%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
  },
};

interface ProductActivityTableProps extends EditableDragTableProps {
  tableRef?: EditableDragTableRef;
  dataSource: ActivityItemEntity[];
  onValuesChange: (allValues: any) => void;
  activityType: ActivityType;
}
const ProductActivityTable: React.FC<ProductActivityTableProps> = (props) => {
  const { dataSource = [], onDataChange, onValuesChange, columns, tableRef, activityType } = props;

  const searchParams = new URLSearchParams(window.location.search);
  const type = searchParams.get('type'); // view | edit | copy
  const readonly = type === 'view';

  const intl = useIntl();
  const [isModalVisible, setIsModalVisible] = useState(false);

  // 添加商品
  const handleAddProduct = (ids, selectedProducts) => {
    console.log('selectedProducts', selectedProducts);
    const newItems = selectedProducts
      .filter((item) => item?.itemId)
      .map((item) => ({
        itemId: item.itemId,
        itemSn: item.itemSn,
        itemName: item.itemName,
        categoryId: item.categoryId,
        categoryName: item.categoryName,
        brandId: item.brandId,
        brandName: item.brandName,
        suggestPrice: item.suggestPrice,
        promotionPrice: undefined,
        minBuyNum: 1,
        maxBuyNum: undefined,
        totalMaxBuyNum: undefined,
        buySelfNum: undefined,
        giftSelfNum: undefined,
        giftTotalMaxBuyNum: undefined,
      }));
    const updatedItems = uniqBy([...newItems, ...dataSource], 'itemId');
    onDataChange?.(updatedItems);
    setIsModalVisible(false);
  };

  // 删除选中商品
  const handleDeleteSelected = () => {
    const selectedRows = tableRef?.current?.getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
      return;
    }
    const newDataSource = dataSource.filter(
      (item) => !selectedRows.some((selected) => selected.itemId === item.itemId),
    );
    onDataChange?.(newDataSource);
    tableRef?.current?.clearSelection();
  };

  const onImportSuccess = (taskId: number) => {
    console.log('taskId', taskId);
    queryActivityItemByTaskId({ taskId }).then((result) => {
      console.log('queryActivityItemByTaskId', result);
      onDataChange?.(result);
    });
  };

  const handleImport = () => {
    importData({
      moduleId: activityItemImport[activityType].moduleId,
      systemId: 'GRIPX_STORE_SYS',
      taskDesc: intl.formatMessage({ id: activityItemImport[activityType].taskDesc }),
      downloadFileName: activityItemImport[activityType].downloadFileName,
      intl,
      onSuccess: onImportSuccess,
    });
  };

  const processedDataSource = useMemo(() => {
    return dataSource.map((item, index) => ({
      ...item,
      showOrder: index + 1,
      rowSpan: dataSource.length,
    }));
  }, [dataSource]);

  return (
    <>
      {!readonly && (
        <Space className="mb-4">
          <Button type="primary" onClick={() => setIsModalVisible(true)}>
            {intl.formatMessage({ id: 'shop.common.button.addProduct' })}
          </Button>
          <Button className="button-outline" onClick={handleDeleteSelected}>
            {intl.formatMessage({ id: 'common.button.delete' })}
          </Button>
          <Button className="button-outline" onClick={handleImport}>
            {intl.formatMessage({ id: 'common.import.title' })}
          </Button>
        </Space>
      )}

      <EditableDragTable
        ref={tableRef}
        columns={columns}
        dataSource={processedDataSource}
        onDataChange={onDataChange}
        rowKey="itemId"
        maxHeight={600}
        dragSortKey="showOrder"
        readonly={readonly}
        onValuesChange={onValuesChange}
      />

      <SelectProductDrawer
        selected={dataSource.map((item) => item.itemId)}
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
        }}
        onOk={handleAddProduct}
      />
    </>
  );
};

export default ProductActivityTable;
