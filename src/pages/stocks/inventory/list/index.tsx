import GoodsPrintDrawer, { PrintItems } from '@/components/GoodsPrintDrawer';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { exportData } from '@/utils/exportData';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { useIntl, useLocation } from '@umijs/max';
import { useAsyncEffect, useBoolean } from 'ahooks';
import { Button, Flex, Space, message } from 'antd';
import { forEach, isEmpty } from 'lodash';
import React, { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { warehouseList } from '../../warehouse/services';
import {
  inventorysafetylimitPost,
  itemLocationRemarkPost,
  queryInventoryPagePost,
  queryTotalPost,
} from '../services';
import DetailDrawer from './components/DetailDrawer';
import { PostListTableColumns } from './config/postListTableColumns';
import type { ChangeDetailRequest } from './types/change.detail.request';
import type { InventoryPostEntity } from './types/inventory.post.entity';
import { SetupTypeStatus } from './types/setupTypeStatus';
import type { TotalPostEntity } from './types/total.post.entity';

const OutputList = () => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const location = useLocation();
  const [requestParams, setRequestParams] = useState<any>();
  const [showPrint, setShowPrint] = useState(false);
  const [wareHouseOptions, setWareHouseOptions] = useState<{ label: string; value: string }[]>([]);
  useAsyncEffect(async () => {
    const params = {};
    if (location?.state?.invLimitStatusList) {
      const invLimitStatusList = location?.state?.invLimitStatusList;
      setRequestParams((pre) => ({ ...pre, invLimitStatusList }));
      formRef.current?.setFieldValue('invLimitStatusList', invLimitStatusList);
    }
    if (location?.state?.storeIdList) {
      const storeIdList = location?.state?.storeIdList;
      const storeId = storeIdList[0];
      params.storeIdList = storeIdList;
      setRequestParams((pre) => ({ ...pre, storeId }));
      formRef.current?.setFieldValue('storeIdList', storeIdList);
    }
    const { warehouseSimpleRoList } = await warehouseList(params);
    if (warehouseSimpleRoList) {
      const options: { label: string; value: string }[] = [];
      const defaultSelectWarehouseIdList: string[] = [];
      forEach(warehouseSimpleRoList, (value) => {
        options.push({ label: value.warehouseName!, value: value.id! });
        if (location?.state?.storeIdList) {
          defaultSelectWarehouseIdList.push(value.id!);
        }
      });
      setWareHouseOptions(options);

      formRef.current?.setFieldValue('warehouseName', defaultSelectWarehouseIdList);
    }
  }, [location?.state]);

  const [totalData, setTotalData] = useState<TotalPostEntity>({});

  const [orderDataCache, setOrderDataCache] = useState<InventoryPostEntity[]>([]);

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 查询 方法
   * @param params
   * @returns
   */
  const loadData = async (params: any) => {
    const data = await queryInventoryPagePost(params);
    const dataTotal = await queryTotalPost(params);
    setTotalData(dataTotal);
    return data;
  };

  /**
   * 库存流水
   * @param itemId
   * @param warehouseId
   * @param warehouseName
   */
  const handleDetail = async (itemId: string, warehouseId: string, warehouseName: string) => {
    setDetailModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      recordId: { itemId, warehouseId, warehouseName } as ChangeDetailRequest,
    }));
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  /**
   * 是否点击了 设置
   */
  const [state, { setTrue, setFalse }] = useBoolean(false);

  const [detailDrawer, setDetailModalProps] = useState<CommonModelForm<ChangeDetailRequest, any>>({
    visible: false,
    recordId: undefined,
    title: intl.formatMessage({ id: 'stocks.inventory.detail.title' }),
    onOk: () => Promise.resolve(true),
  });

  /**
   * 关闭提示对话框
   */
  const closeDetailDrawer = () => {
    setDetailModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: undefined,
    }));
  };

  /**
   * 设置类型
   */
  const [setUp, setSetUp] = useState<SetupTypeStatus>();

  const handleSaveSafety = async () => {
    if (!isEmpty(selectedRowKeys)) {
      const itemData = orderDataCache.filter((item) => selectedRowKeys.includes(item.id!));
      if (setUp == SetupTypeStatus.LOCATION) {
        //库位
        const data = await itemLocationRemarkPost({ itemLocationRemarkCmdList: itemData });
        if (data) {
          reloadData();
        }
      } else if (setUp == SetupTypeStatus.SAFETY) {
        //安全库存
        const data = await inventorysafetylimitPost({ inventorySafetyLimitCmdList: itemData });
        if (data) {
          reloadData();
        }
      }
    }
  };
  const reloadData = () => {
    actionRef.current?.reload(true);
    message.success(intl.formatMessage({ id: 'stocks.inventory.list.message.saveSuccess' }));
    setSelectedRowKeys([]);
    setFalse();
    setSetUp(undefined);
  };

  const getPrintItems = (): PrintItems[] => {
    return orderDataCache
      .filter((item) => selectedRowKeys.includes(item.id!))
      .map((item) => ({
        itemSn: item.itemSn!,
        number: item.inventoryNum!,
        warehouseName: item.warehouseName,
      }));
  };

  return (
    <PageContainer>
      <FunProTable<InventoryPostEntity, any>
        rowKey="id"
        onSubmit={(params) => {
          console.log(params);
        }}
        onReset={() => {
          setRequestParams({ pageSize: 10, pageNo: 1 });
        }}
        params={requestParams}
        requestPage={loadData}
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        formRef={formRef}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedKeys) => {
            if (isEmpty(selectedKeys)) {
              //空
              setSetUp(undefined);
              setFalse();
            }
            setSelectedRowKeys(selectedKeys);
          },
        }}
        editable={{
          type: 'multiple',
          editableKeys: selectedRowKeys,
          actionRender: (row, config, defaultDom) => [],
          onValuesChange: (record, recordList) => {
            setOrderDataCache(recordList);
          },
        }}
        headerTitle={
          <Flex vertical>
            <Flex className="text-[14px] text-black/[0.45]">
              <span>
                {intl.formatMessage({ id: 'stocks.inventory.list.label.totalInventory' })}
                <span className="text-[24px] pl-4 text-black/[0.85] font-semibold">
                  {totalData?.totalInventory}
                </span>
              </span>
              <span className="pl-[80px]">
                {intl.formatMessage({ id: 'stocks.inventory.list.label.totalCostPrice' })}
                <span className="text-[24px] pl-4 text-black/[0.85] font-semibold">
                  {totalData?.totalCostPrice}
                </span>
              </span>
            </Flex>
            <span className="py-4" style={{ border: 1 }} />
            <Flex>
              {(!state || selectedRowKeys.length == 0) && (
                <Space>
                  <AuthButton
                    key="safety"
                    authority="setSafetyInventory"
                    disabled={selectedRowKeys.length <= 0}
                    onClick={() => {
                      setSetUp(SetupTypeStatus.SAFETY);
                      setTrue();
                    }}
                  >
                    {intl.formatMessage({ id: 'stocks.inventory.list.button.setSafetyInventory' })}
                  </AuthButton>
                  <AuthButton
                    authority="setLocation"
                    key="location"
                    disabled={selectedRowKeys.length <= 0}
                    onClick={() => {
                      setSetUp(SetupTypeStatus.LOCATION);
                      setTrue();
                    }}
                  >
                    {intl.formatMessage({ id: 'stocks.inventory.list.button.setLocation' })}
                  </AuthButton>
                  <AuthButton
                    authority="inventoryPrintGoodTag"
                    disabled={selectedRowKeys.length <= 0}
                    onClick={() => setShowPrint(true)}
                  >
                    {intl.formatMessage({ id: 'stocks.inventory.list.button.printLabel' })}
                  </AuthButton>
                  <AuthButton
                    authority="exportInventory"
                    onClick={() => {
                      exportData({
                        systemId: 'GRIPX_STORE_SYS',
                        taskDesc: intl.formatMessage({ id: 'stocks.inventory.list.button.export' }),
                        moduleId: 'INVENTORY_DATA_EXPORT',
                        params: {
                          ...formRef.current?.getFieldsValue(),
                          warehouseIdList: formRef.current?.getFieldValue('warehouseName'),
                        },
                      });
                    }}
                  >
                    {intl.formatMessage({ id: 'stocks.inventory.list.button.export' })}
                  </AuthButton>
                  <AuthButton
                    key="import"
                    authority="importInventory"
                    onClick={() => {
                      importData({
                        moduleId: 'INVENTORY_INIT_IMPORT',
                        systemId: 'GRIPX_STORE_SYS',
                        taskDesc: intl.formatMessage({
                          id: 'stocks.inventory.list.button.importInitialInventory',
                        }),
                        downloadFileName:
                          'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E6%9C%9F%E5%88%9D%E5%BA%93%E5%AD%98%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
                      });
                    }}
                  >
                    {intl.formatMessage({
                      id: 'stocks.inventory.list.button.importInitialInventory',
                    })}
                  </AuthButton>
                </Space>
              )}
              {state && selectedRowKeys.length > 0 && (
                <Button key="save" onClick={handleSaveSafety}>
                  {intl.formatMessage({ id: 'stocks.inventory.list.button.save' })}
                </Button>
              )}
            </Flex>
          </Flex>
        }
        search={{ labelWidth: 100, defaultCollapsed: false }}
        onDataSourceChange={(dataSource) => {
          setOrderDataCache(dataSource);
        }}
        columns={PostListTableColumns({
          wareHouseOptions,
          handleDetail,
          setUp,
          state,
        })}
      />
      <DetailDrawer {...detailDrawer} onCancel={closeDetailDrawer} />
      <GoodsPrintDrawer
        visible={showPrint}
        onClose={() => setShowPrint(false)}
        items={getPrintItems()}
        bizType={'inventory'}
      />
    </PageContainer>
  );
};
export default withKeepAlive(OutputList);
