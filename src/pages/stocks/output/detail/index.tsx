import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { CommonModelForm } from '@/types/CommonModelForm';
import {
  PageContainer,
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Space, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import OutputModal from '../list/components/OutputModal';
import { OutPutBillTypeStatusOptions } from '../list/types/OutPutBillTypeStatus';
import { OutPutStatus, outPutStatusOptions } from '../list/types/OutPutStatus';
import { OutPutPostEntity } from '../list/types/output.post.entity';
import { confirmOutPutPost, queryOutPutDetailPost } from '../services';
import { OutPutPostListTableColumns } from './config/outputPostListTableColumns';
import { OutPutDetailPostEntity } from './types/output.detail.post.entity';
export default () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ProDescriptionsActionType>();
  const searchParams = new URLSearchParams(window.location.search);
  let [recordData, setRecordData] = useState<OutPutDetailPostEntity>({});

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const [outputModalProps, setOutputModalProps] = useState<
    CommonModelForm<string, OutPutPostEntity>
  >({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });
  /**
   * 出库
   */
  /**
   * 出库页面
   * @param ids
   */
  const handleOutPut = async (id: string, warehouseId: string) => {
    setOutputModalProps({
      visible: true,
      recordId: id,
      title: intl.formatMessage({ id: 'stocks.output.modal.title.output' }),
    });
  };

  const hanleConfirm = async (values: any) => {
    const data = await confirmOutPutPost(values);
    if (data) {
      hideModal();
      actionRef?.current?.reload();
    }
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setOutputModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
  };

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Space>
              <span>{recordData?.stockOutRo?.bizBillNo}</span>
              <Tag color="success">{outPutStatusOptions[recordData?.stockOutRo?.state!]?.text}</Tag>
            </Space>
          }
          extra={
            <Space>
              <AuthButton
                authority="outWarehousePrint"
                onClick={() => {
                  window.open(
                    `/print?stockOutId=${searchParams.get('outputId')}&printType=${
                      PrintType.outStockOrder
                    }`,
                  );
                }}
              >
                {intl.formatMessage({ id: 'stocks.output.detail.button.print' })}
              </AuthButton>
              {[
                OutPutStatus.AUDITING,
                OutPutStatus.TO_ARRIVAL,
                OutPutStatus.PICKING_IN_PROGRESS,
                OutPutStatus.PICKING_COMPLETED,
              ].includes(recordData?.stockOutRo?.state) && (
                <AuthButton
                  type="primary"
                  authority="outWarehouse"
                  onClick={() =>
                    handleOutPut(recordData?.stockOutRo?.id!, recordData?.stockOutRo?.warehouseId!)
                  }
                >
                  {intl.formatMessage({ id: 'stocks.output.list.button.output' })}
                </AuthButton>
              )}
            </Space>
          }
          params={{
            stockOutId: searchParams.get('outputId'),
            warehouseId: searchParams.get('outputWarehouseId'),
          }}
          request={async (params) => {
            if (!isEmpty(params?.stockOutId) && !isEmpty(params?.warehouseId)) {
              const data = await queryOutPutDetailPost({ ...params });
              if (data) {
                setRecordData(data);
              }
              return { data, success: true };
            }
            return Promise.resolve({
              success: false,
              data: {},
            });
          }}
          column={4}
        >
          <ProDescriptions.Item
            dataIndex={['stockOutRo', 'origBillNo']}
            label={intl.formatMessage({ id: 'stocks.output.detail.label.businessOrderNo' })}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.output.detail.label.outputType' })}
            dataIndex={['stockOutRo', 'billType']}
            valueEnum={OutPutBillTypeStatusOptions}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.output.detail.label.notifyOutputTime' })}
            dataIndex={['stockOutRo', 'createTime']}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.output.detail.label.outputCompleteTime' })}
            dataIndex={['stockOutRo', 'realOutTime']}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.output.detail.label.outputWarehouse' })}
            dataIndex={['stockOutRo', 'warehouseName']}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.output.detail.label.outputQuantity' })}
            dataIndex={['stockOutRo', 'totalAmount']}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.output.detail.label.recipient' })}
            dataIndex={['stockOutRo', 'customer']}
          />
        </ProDescriptions>
      </ProCard>
      <FunProTable
        className="mt-4"
        headerTitle={
          <SubTitle
            text={intl.formatMessage({ id: 'stocks.output.detail.subtitle.goodsDetail' })}
          />
        }
        columns={OutPutPostListTableColumns({ isDetail: true, isEdit: false })}
        rowKey="id"
        search={false}
        pagination={false}
        dataSource={recordData?.stockOutDetailRoList}
        scroll={{ x: 'max-content' }}
      />
      {recordData?.stockOutRo?.distributionModeDesc && (
        <ProCard
          title={
            <SubTitle
              text={intl.formatMessage({ id: 'stocks.output.detail.subtitle.deliveryInfo' })}
            />
          }
          className="mt-4"
        >
          <ProDescriptions actionRef={actionRef} column={4} dataSource={recordData}>
            <ProDescriptions.Item
              label={intl.formatMessage({ id: 'stocks.output.detail.label.deliveryAddress' })}
              dataIndex={['stockOutRo', 'deliveryAddress']}
            />
            <ProDescriptions.Item
              label={intl.formatMessage({ id: 'stocks.output.detail.label.deliveryMethod' })}
              dataIndex={['stockOutRo', 'distributionModeDesc']}
            />
            <ProDescriptions.Item
              label={intl.formatMessage({ id: 'stocks.output.detail.label.deliveryCompany' })}
              dataIndex={['stockOutRo', 'logisticsCompanyName']}
            />
            <ProDescriptions.Item
              label={intl.formatMessage({ id: 'stocks.output.detail.label.logisticsNo' })}
              dataIndex={['stockOutRo', 'logisticsNo']}
            />
          </ProDescriptions>
        </ProCard>
      )}

      <OutputModal {...outputModalProps} onOk={hanleConfirm} onCancel={hideModal} />
    </PageContainer>
  );
};
