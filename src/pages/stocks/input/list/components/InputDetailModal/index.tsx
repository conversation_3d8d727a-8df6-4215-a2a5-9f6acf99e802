import ConfirmModal from '@/components/ConfirmModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { DrawerForm, ProCard, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import type { GetProps } from 'antd';
import { Button } from 'antd';
import { useState } from 'react';
import { InputPostListTableColumns } from '../../../detail/config/inputPostListTableColumns';
import type { StockInDetailRoList } from '../../../detail/types/input.detail.post.entity';
import { queryBatchDetailPost, withdrawInputPost } from '../../../services';
import type { InputPostWithdrawRequest } from '../../types/input.post.withdraw.request';
import type { InputStockBatchList, StockInBatchRoList } from '../../types/input.stock.batch.list';

export default (props: CommonModelForm<string, StockInDetailRoList>) => {
  const intl = useIntl();
  const [recordData, setRecordData] = useState<InputStockBatchList>({});
  useAsyncEffect(async () => {
    if (props.recordId) {
      loadData();
    }
  }, [props.visible]);

  const loadData = async () => {
    const data = await queryBatchDetailPost({ stockInId: props.recordId });
    setRecordData(data);
  };

  const handleWithdrawInput = async (values: InputPostWithdrawRequest) => {
    const data = await withdrawInputPost(values);
    if (data) {
      hideConfirmModal();
      loadData();
    }
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const hideConfirmModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      layout="horizontal"
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        onClose: props.onCancel,
        maskClosable: false,
      }}
      submitter={false}
      open={props.visible}
      validateTrigger={'onchang'}
    >
      <ProFormText name="id" hidden />
      <ProFormText name="origBillNo" hidden />
      {recordData?.stockInBatchRoList?.map((s) => (
        <ProCard
          className="mb-4"
          bodyStyle={{ padding: 0 }}
          title={
            <div className="text-[14px] font-normal text-black/[0.85]">
              <span>
                {intl.formatMessage({ id: 'stocks.input.modal.label.inputTime' })}：{s.stockInTime}
              </span>
              <span className="pl-[40px]">
                {intl.formatMessage({ id: 'stocks.input.modal.label.operator' })}：{s.stockInPerson}
              </span>
              {s.cancelTime && (
                <>
                  <span className="pl-[80px]">
                    {intl.formatMessage({ id: 'stocks.input.modal.label.voidTime' })}：
                    {s.cancelTime}
                  </span>
                  <span className="pl-[40px]">
                    {intl.formatMessage({ id: 'stocks.input.modal.label.operator' })}：
                    {s.cancelPerson}
                  </span>
                </>
              )}
            </div>
          }
          extra={
            <>
              {s.state == 0 && (
                <Button>{intl.formatMessage({ id: 'stocks.input.modal.button.voided' })}</Button>
              )}
              {s.state == 1 && (
                <AuthButton
                  authority="inWarehouseDelete"
                  onClick={() =>
                    setConfirmModalProps({
                      open: true,
                      tips: intl.formatMessage({ id: 'stocks.input.modal.confirm.void' }),
                      onOk: () =>
                        handleWithdrawInput({
                          id: s.id,
                          stockInId: s.stockInId,
                          origBillNo: s.origBillNo,
                        }),
                    })
                  }
                >
                  {intl.formatMessage({ id: 'stocks.input.modal.button.void' })}
                </AuthButton>
              )}
            </>
          }
        >
          <FunProTable<StockInBatchRoList, any>
            rowKey="id"
            scroll={{ x: 1300 }}
            search={false}
            options={false}
            pagination={false}
            dataSource={s?.stockInBatchDetailRoList}
            columns={InputPostListTableColumns({ onShow: true })}
          />
        </ProCard>
      ))}
      <ConfirmModal {...confirmModalProps} onCancel={hideConfirmModal} />
    </DrawerForm>
  );
};
