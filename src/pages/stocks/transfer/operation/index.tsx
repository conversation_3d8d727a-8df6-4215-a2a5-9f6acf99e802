import AuthButton from '@/components/common/AuthButton';
import SubTitle from '@/components/common/SubTitle';
import { useKeepAliveTabs } from '@/layouts/useKeepAliveTabs';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import {
  TransferBillType,
  TransferStatusNameOptions,
} from '@/pages/stocks/transfer/list/types/TransferStatusEnum';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { importData } from '@/utils/importData';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType, EditableFormInstance, ProFormInstance } from '@ant-design/pro-components';
import {
  EditableProTable,
  PageContainer,
  ProCard,
  ProForm,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { useAsyncEffect, useDebounceFn } from 'ahooks';
import { Checkbox, Flex, Input, Space, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty, uniqueId } from 'lodash';
import React, { useRef, useState } from 'react';
import { warehouseList } from '../../warehouse/services';
import type { TransferDetailPostEntity } from '../detail/types/transfer.detail.post.entity';
import type { TransferPostEntity } from '../list/types/transfer.post.entity';
import {
  changeWarehousePost,
  deleteDetailPost,
  queryTransferByIdOrNo,
  queryTransferPostDetail,
  submitTransferPost,
  transferCreateOrUpdatePost,
} from '../services';
import GoodsModal from './components/goodsModal';
import { TransferListTableColumns } from './config/transferListTableColumns';
const TransferOperationList = () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ActionType>();
  const { Search } = Input;
  const searchParams = new URLSearchParams(window.location.search);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const transferId = searchParams.get('transferId') ?? '';
  const bizBillNo = searchParams.get('bizBillNo') ?? '';
  const formRef = useRef<ProFormInstance>();
  const formRefRemarks = useRef<ProFormInstance>();
  const formRefTable = useRef<EditableFormInstance>();

  const [detailData, setDetailData] = useState<TransferPostEntity>({});
  const [directStockOut, setDirectStockOut] = useState<boolean>(false); //是否一键出库
  const [keyword, setKeyword] = useState<string>();
  const [tabKey, setTabKey] = useState('tabKey_001');

  const storeIdIn = ProForm.useWatch('storeIdIn', formRef.current ?? {});
  const warehouseIdIn = ProForm.useWatch('warehouseIdIn', formRef.current ?? {});
  const storeIdOut = ProForm.useWatch('storeIdOut', formRef.current ?? {});
  const warehouseIdOut = ProForm.useWatch('warehouseIdOut', formRef.current ?? {});
  const remarks = ProForm.useWatch('remarks', formRefRemarks.current ?? {});

  const { closeTab } = useKeepAliveTabs();

  const [goodsModalProps, setGoodsModalProps] = useState<
    CommonModelForm<string, TransferPostEntity>
  >({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });

  useAsyncEffect(async () => {
    if (!isEmpty(transferId)) {
      const data = await queryTransferByIdOrNo({ bizBillNo, id: transferId });
      if (data) {
        setDetailData(data);
        formRef?.current?.setFieldsValue(data);
        formRefRemarks?.current?.setFieldsValue(data);
      }
    } else {
      setDetailData({});
      formRef?.current?.resetFields();
      formRefRemarks?.current?.resetFields();
    }
  }, [transferId]);

  /**
   * 添加商品
   */
  const handleAddGoods = () => {
    formRef.current?.validateFields().then(async (result) => {
      setGoodsModalProps({
        visible: true,
        recordId: result?.warehouseIdOut,
        title: intl.formatMessage({ id: 'stocks.transfer.operation.modal.title.addGoods' }),
      });
    });
  };

  /**
   * 监听门店、仓库变化
   * @param changedValues
   */
  const handleWarehouseChange = async (changedValues: any) => {
    if (
      transferId &&
      changedValues.warehouseIdIn &&
      changedValues.warehouseIdIn == warehouseIdOut
    ) {
      message.error(intl.formatMessage({ id: 'stocks.transfer.operation.message.warehouseSame' }));
      return;
    }
    if (transferId) {
      const data = await changeWarehousePost({ id: transferId, bizBillNo, ...changedValues });
      if (data) {
        actionRef?.current?.reload();
      }
    }
  };

  /**
   * 隐藏添加商品弹窗
   */
  const hideModal = () => {
    setGoodsModalProps({ visible: false, recordId: '0', readOnly: false, title: '' });
  };

  /**
   * 调拨商品列表数据变化
   */
  const handleTransferFormDataChange = async (changedValues: any, allValues: any) => {
    const rowData = formRefTable.current?.getRowData?.(changedValues.id);
    if (rowData?.transferNum == undefined) {
      rowData.transferNum = 0;
    }
    if (rowData && rowData?.transferNum != undefined) {
      const data = await transferCreateOrUpdatePost({
        id: isEmpty(transferId) ? undefined : transferId,
        storeIdIn,
        storeIdOut,
        warehouseIdIn,
        warehouseIdOut,
        stockTransferDetailCmdList: [changedValues],
        // remarks
      });
      if (data) {
        actionRef?.current?.reload();
      }
    } else {
      formRefTable.current?.setRowData?.(changedValues.id, { transferNum: 0 });
    }
    return;
  };

  /**
   * 监听表格变化
   */
  const { run } = useDebounceFn(
    (changedValues: any, allValues: any) => handleTransferFormDataChange(changedValues, allValues),
    { wait: 500 },
  );

  /**
   * 添加商品-确定
   */
  const handleOnSelectGoods = async (values: any) => {
    if (warehouseIdIn === warehouseIdOut) {
      message.error(intl.formatMessage({ id: 'stocks.transfer.operation.message.warehouseSame' }));
      return;
    }
    if (values?.itemId && storeIdIn && warehouseIdIn && warehouseIdOut && storeIdOut) {
      const detailList = values?.itemId.map((s: string) => ({
        itemId: s,
        transferId: isEmpty(transferId) ? undefined : transferId,
      }));
      const data = await transferCreateOrUpdatePost({
        id: isEmpty(transferId) ? undefined : transferId,
        storeIdIn,
        storeIdOut,
        warehouseIdIn,
        warehouseIdOut,
        stockTransferDetailCmdList: detailList,
        billType: TransferBillType.REPLENISHMENT_TRANSFER,
        // remarks
      });
      if (data) {
        if (isEmpty(transferId)) {
          //成功跳编辑
          hideModal();
          history.push(
            '/purchase/transfer/operation?transferId=' + data.id + '&bizBillNo=' + data.bizBillNo,
          );
        } else {
          actionRef?.current?.reload();
          hideModal();
        }
      }
    }
  };

  /**
   * 取消-回到列表页
   */
  const handleCancel = () => {
    closeTab();
    history.push('/purchase/transfer');
  };

  /**
   * 删除
   */
  const handleDeleteItem = async (id: string) => {
    if (!isEmpty(transferId)) {
      const idList = [id];
      const data = await deleteDetailPost({ transferId, idList });
      if (data) {
        message.success(
          intl.formatMessage({ id: 'stocks.transfer.operation.message.deleteSuccess' }),
        );
        actionRef?.current?.reload();
        setTabKey(uniqueId());
      }
    }
  };

  /**
   * 提交
   */
  const handleSubmit = async () => {
    const dataList = formRefTable.current?.getFieldsValue();
    if (Object.keys(dataList).length === 0) {
      message.error(intl.formatMessage({ id: 'stocks.transfer.operation.message.addGoods' }));
      return;
    }
    const hasZeroValue = Object.values(dataList).some(
      (obj) => !obj.transferNum || obj.transferNum === 0,
    );
    if (hasZeroValue) {
      message.warning(intl.formatMessage({ id: 'stocks.transfer.operation.message.fillQuantity' }));
      return;
    }
    if (!isEmpty(transferId)) {
      //不为空
      const data = await submitTransferPost({ id: transferId, bizBillNo, remarks, directStockOut });
      if (data) {
        closeTab();
        history.push('/purchase/transfer/detail?transferId=' + transferId);
      }
    }
  };

  const importFn = (data) => {
    importData({
      moduleId: 'TRANSFER_DETAIL_IMPORT',
      systemId: 'GRIPX_STORE_SYS',
      taskDesc: '调拨商品批量导入',
      downloadFileName:
        'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%BA%93%E4%BD%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
      params: {
        bizBillNo: data.bizBillNo,
      },
      onSuccess() {
        actionRef?.current?.reload();
      },
    });
  };
  const handleImport = async () => {
    if (warehouseIdIn === warehouseIdOut) {
      message.error(intl.formatMessage({ id: 'stocks.transfer.operation.message.warehouseSame' }));
      return;
    }
    if (bizBillNo) {
      importFn({
        id: transferId,
        bizBillNo,
      });
    } else {
      const data = await transferCreateOrUpdatePost({
        storeIdIn,
        storeIdOut,
        warehouseIdIn,
        warehouseIdOut,
        billType: TransferBillType.REPLENISHMENT_TRANSFER,
      });
      history.push(
        '/purchase/transfer/operation?transferId=' + data.id + '&bizBillNo=' + data.bizBillNo,
      );
      if (data) {
        importFn(data);
      }
    }
  };

  return (
    <PageContainer>
      <ProCard>
        <ProForm
          labelAlign={'left'}
          layout="inline"
          submitter={false}
          formRef={formRef}
          onValuesChange={handleWarehouseChange}
        >
          <ProFormSelect
            name="storeIdIn"
            label={intl.formatMessage({ id: 'stocks.transfer.operation.label.inStore' })}
            allowClear={false}
            rules={[REQUIRED_RULES]}
            fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
            request={(params) =>
              queryStoreByAccount({ status: 1 }).then((result) => {
                if (transferId) {
                  formRef?.current?.setFieldsValue?.({
                    storeIdIn: detailData.storeIdIn,
                  });
                } else {
                  // 新增时 默认带出调出门店
                  let defaultItem;
                  defaultItem = result?.find((item) => item.type === '0');
                  if (!defaultItem) {
                    defaultItem = result?.[0];
                  }
                  if (defaultItem) {
                    formRef?.current?.setFieldsValue?.({
                      storeIdOut: defaultItem.id,
                      storeIdIn: defaultItem.id,
                    });
                  }
                }
                return result;
              })
            }
            width="sm"
            style={{ marginBottom: 12 }}
          />
          <ProFormSelect
            name="warehouseIdIn"
            label={intl.formatMessage({ id: 'stocks.transfer.operation.label.inWarehouse' })}
            allowClear={false}
            rules={[REQUIRED_RULES]}
            fieldProps={{ fieldNames: { label: 'warehouseName', value: 'warehouseId' } }}
            dependencies={['storeIdIn']}
            request={(params) => {
              if (params.storeIdIn) {
                return warehouseList({
                  state: YesNoStatus.YES,
                  storeIdList: [params.storeIdIn],
                }).then((result) => {
                  // 判断是否为新增调拨 或者编辑状态下 修改了调入门店
                  if (!transferId || params.storeIdIn !== detailData.storeIdIn) {
                    let defaultItem;
                    defaultItem = result?.warehouseStoreRelationRoList?.find(
                      (item) => item.isDefault,
                    );
                    if (!defaultItem) {
                      defaultItem = result?.warehouseStoreRelationRoList?.[0];
                    }
                    formRef?.current?.setFieldsValue?.({
                      warehouseIdIn: defaultItem?.warehouseId ?? '',
                    });
                  } else {
                    formRef?.current?.setFieldsValue?.({
                      warehouseIdIn: detailData.warehouseIdIn,
                    });
                  }
                  return result.warehouseStoreRelationRoList ?? [];
                });
              } else {
                return new Promise((resolve, reject) => {
                  return reject([]);
                });
              }
            }}
            width="sm"
            style={{ marginBottom: 12 }}
          />
          <ProFormSelect
            name="storeIdOut"
            label={intl.formatMessage({ id: 'stocks.transfer.operation.label.outStore' })}
            allowClear={false}
            rules={[REQUIRED_RULES]}
            fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
            request={() =>
              queryStoreByAccount({ status: 1 }).then((result) => {
                return result;
              })
            }
            width="sm"
            style={{ marginBottom: 12 }}
          />
          <ProFormSelect
            name="warehouseIdOut"
            label={intl.formatMessage({ id: 'stocks.transfer.operation.label.outWarehouse' })}
            allowClear={false}
            rules={[REQUIRED_RULES]}
            fieldProps={{ fieldNames: { label: 'warehouseName', value: 'warehouseId' } }}
            dependencies={['storeIdOut']}
            request={(params) => {
              if (params.storeIdOut) {
                return warehouseList({
                  state: YesNoStatus.YES,
                  storeIdList: [params.storeIdOut],
                }).then((result) => {
                  // 判断是否为新增调拨 或者编辑状态下 修改了调出门店
                  if (!transferId || params.storeIdOut !== detailData.storeIdOut) {
                    let defaultItem;
                    defaultItem = result?.warehouseStoreRelationRoList?.find(
                      (item) => item.isDefault,
                    );
                    if (!defaultItem) {
                      defaultItem = result?.warehouseStoreRelationRoList?.[0];
                    }
                    formRef?.current?.setFieldsValue?.({
                      warehouseIdOut: defaultItem?.warehouseId ?? '',
                    });
                  } else {
                    formRef?.current?.setFieldsValue?.({
                      warehouseIdOut: detailData.warehouseIdOut,
                    });
                  }
                  return result.warehouseStoreRelationRoList ?? [];
                });
              } else {
                return new Promise((resolve, reject) => {
                  return reject([]);
                });
              }
            }}
            width="sm"
            style={{ marginBottom: 12 }}
          />
        </ProForm>
      </ProCard>
      <ProCard
        title={
          <Space>
            <SubTitle
              text={intl.formatMessage({ id: 'stocks.transfer.operation.subtitle.transferGoods' })}
            />
            {detailData?.bizBillNo && (
              <span className="font-normal text-base text-[14px] text-black/[0.8] pl-4">
                {intl.formatMessage({ id: 'stocks.transfer.operation.label.transferOrderNo' })}：
                {detailData?.bizBillNo}
              </span>
            )}
            {TransferStatusNameOptions[detailData?.state!]?.text && (
              <span className="font-normal text-base text-[14px] text-black/[0.8]">
                {intl.formatMessage({ id: 'stocks.transfer.operation.label.documentStatus' })}：
                {TransferStatusNameOptions[detailData?.state!]?.text}
              </span>
            )}
          </Space>
        }
        className="mt-4"
        bodyStyle={{ padding: 0 }}
      >
        <EditableProTable<TransferDetailPostEntity>
          rowKey="id"
          search={false}
          columns={TransferListTableColumns({ handleDeleteItem })}
          actionRef={actionRef}
          key={tabKey}
          editableFormRef={formRefTable}
          scroll={{ x: 1300, y: 'max-content' }}
          pagination={{
            showQuickJumper: true,
            defaultPageSize: 10,
            showSizeChanger: false,
          }}
          title={() => (
            <Flex justify="space-between">
              <Space>
                <AuthButton
                  type="primary"
                  key="addTransferPart"
                  authority="addTransferPart"
                  onClick={() => handleAddGoods()}
                >
                  {intl.formatMessage({ id: 'stocks.transfer.operation.button.addGoods' })}
                </AuthButton>
                <AuthButton key="import" onClick={handleImport}>
                  {intl.formatMessage({ id: 'common.button.batchImport' })}
                </AuthButton>
              </Space>
              <Space>
                <Search
                  placeholder={intl.formatMessage({
                    id: 'stocks.transfer.operation.placeholder.goodsCodeOrName',
                  })}
                  allowClear
                  onSearch={setKeyword}
                  style={{ width: 237 }}
                />
              </Space>
            </Flex>
          )}
          recordCreatorProps={false}
          params={{ keyword, transferId, warehouseIdOut, isEdit: transferId ? true : false }}
          request={async (params) => {
            if (!isEmpty(transferId) && !isEmpty(warehouseIdOut)) {
              const result = await queryTransferPostDetail(params);
              const { data } = result;
              setEditableRowKeys(data?.map((item) => item.id!));
              return result;
            }
            return { data: [], success: true };
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
            actionRender: (row, config, defaultDoms) => {
              return [];
            },
            onValuesChange: run,
          }}
        />
      </ProCard>
      <ProCard className="mt-4">
        <ProForm labelAlign={'left'} layout="horizontal" submitter={false} formRef={formRefRemarks}>
          <ProFormTextArea
            name="remarks"
            label={intl.formatMessage({ id: 'stocks.transfer.operation.label.transferRemarks' })}
            placeholder={intl.formatMessage({
              id: 'stocks.transfer.operation.placeholder.transferRemarks',
            })}
            fieldProps={{
              count: { max: 100, show: true },
              maxLength: 100,
            }}
            rules={[{ max: 100 }]}
          />
        </ProForm>
      </ProCard>
      <ProCard className="mt-[1px]">
        <Flex justify="flex-end" align="center">
          <Space>
            <Checkbox
              className="mb-0"
              onChange={(e) => {
                if (e.target?.checked) {
                  setDirectStockOut(true);
                } else {
                  setDirectStockOut(false);
                }
              }}
            >
              {intl.formatMessage({ id: 'stocks.transfer.operation.checkbox.directStockOut' })}
            </Checkbox>
            <AuthButton authority="" key="cancel" onClick={handleCancel}>
              {intl.formatMessage({ id: 'stocks.transfer.operation.button.cancel' })}
            </AuthButton>
            <AuthButton
              authority="submitTransfer"
              key="submitTransfer"
              type="primary"
              onClick={handleSubmit}
            >
              {intl.formatMessage({ id: 'stocks.transfer.operation.button.submit' })}
            </AuthButton>
          </Space>
        </Flex>
      </ProCard>
      <GoodsModal {...goodsModalProps} onCancel={hideModal} onOk={handleOnSelectGoods} />
    </PageContainer>
  );
};

export default withKeepAlive(TransferOperationList);
