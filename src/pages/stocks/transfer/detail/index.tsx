import ConfirmModal from '@/components/ConfirmModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { useKeepAliveTabs } from '@/layouts/useKeepAliveTabs';
import type { ProDescriptionsActionType } from '@ant-design/pro-components';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import type { GetProps } from 'antd';
import { Space, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { TransferStatusEnum, TransferStatusNameOptions } from '../list/types/TransferStatusEnum';
import type { TransferPostEntity } from '../list/types/transfer.post.entity';
import {
  cancelTransferPost,
  queryTransferByIdOrNo,
  queryTransferPostDetail,
  stockInPost,
  stockOutPost,
} from '../services';
import { TransferDetailPostListTableColumns } from './config/transferDetailPostListTableColumns';
import type { TransferDetailPostEntity } from './types/transfer.detail.post.entity';
import type { TransferSimpleRequest } from './types/transfer.detail.request';

export default () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ProDescriptionsActionType>();
  const searchParams = new URLSearchParams(window.location.search);
  const transferId = searchParams.get('transferId');
  const [recordData, setRecordData] = useState<TransferPostEntity>({});
  const { closeTab } = useKeepAliveTabs();

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const hideConfirmModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  /**
   * 确认作废
   */
  const handleTransferCancel = async (values: TransferSimpleRequest) => {
    const data = await cancelTransferPost(values);
    if (data) {
      await hideConfirmModal();
      actionRef?.current?.reload();
    }
  };

  /**
   * 跳转到编辑页面
   */
  const handleContinueEdit = () => {
    closeTab();
    history.push(
      '/purchase/transfer/operation?transferId=' +
        transferId +
        '&bizBillNo=' +
        recordData.bizBillNo,
    );
  };

  /**
   * 一键出库
   */
  const handleStockOut = async () => {
    const data = await stockOutPost({ id: transferId, bizBillNo: recordData?.bizBillNo });
    if (data) {
      actionRef?.current?.reload();
    }
  };

  /**
   * 一键入库
   */
  const handleStockIn = async () => {
    const data = await stockInPost({ id: recordData?.id, bizBillNo: recordData?.bizBillNo });
    if (data) {
      actionRef?.current?.reload();
    }
  };

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Space>
              <span>{recordData?.bizBillNo}</span>
              <Tag color={TransferStatusNameOptions[recordData?.state!]?.status}>
                {TransferStatusNameOptions[recordData?.state!]?.text}
              </Tag>
            </Space>
          }
          extra={
            <Space>
              {(TransferStatusEnum.DRAFT == recordData?.state ||
                TransferStatusEnum.PENDING_SHIPMENT == recordData?.state) && (
                <AuthButton
                  authority="deleteWarehouseTransfer"
                  onClick={() =>
                    setConfirmModalProps({
                      open: true,
                      tips: intl.formatMessage({ id: 'stocks.transfer.detail.confirm.void' }),
                      onOk: () =>
                        handleTransferCancel({
                          id: recordData?.id,
                          bizBillNo: recordData?.bizBillNo,
                        }),
                    })
                  }
                >
                  {intl.formatMessage({ id: 'stocks.transfer.detail.button.void' })}
                </AuthButton>
              )}
              {TransferStatusEnum.DRAFT == recordData?.state && (
                <AuthButton authority="editWarehouseTransfer" onClick={handleContinueEdit}>
                  {intl.formatMessage({ id: 'stocks.transfer.detail.button.edit' })}
                </AuthButton>
              )}
              {TransferStatusEnum.PENDING_SHIPMENT == recordData?.state && (
                <AuthButton
                  authority="oneClickStockOutTransfer"
                  type="primary"
                  onClick={handleStockOut}
                >
                  {intl.formatMessage({ id: 'stocks.transfer.detail.button.oneClickStockOut' })}
                </AuthButton>
              )}
              {TransferStatusEnum.PENDING_ARRIVAL == recordData?.state && (
                <AuthButton
                  authority="oneClickStockInTransfer"
                  type="primary"
                  onClick={handleStockIn}
                >
                  {intl.formatMessage({ id: 'stocks.transfer.detail.button.oneClickStockIn' })}
                </AuthButton>
              )}
            </Space>
          }
          params={{
            id: transferId,
          }}
          request={async (params) => {
            if (!isEmpty(params?.id)) {
              const data = await queryTransferByIdOrNo({ ...params });
              if (data) {
                setRecordData(data);
              }
              return { data, success: true };
            }
            return Promise.resolve({
              success: false,
              data: {},
            });
          }}
          column={4}
        >
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.outStore' })}
            dataIndex="storeIdOutName"
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.outWarehouse' })}
            dataIndex="warehouseIdOutName"
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.inStore' })}
            dataIndex="storeIdInName"
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.inWarehouse' })}
            dataIndex="warehouseIdInName"
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.createTime' })}
            dataIndex="createTime"
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.creator' })}
            dataIndex="createDocPerson"
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.transfer.detail.label.remarks' })}
            dataIndex="remarks"
            span={4}
            ellipsis
          />
        </ProDescriptions>
      </ProCard>
      <FunProTable<TransferDetailPostEntity, any>
        className="mt-4"
        headerTitle={
          <SubTitle
            text={intl.formatMessage({ id: 'stocks.transfer.detail.subtitle.transferGoods' })}
          />
        }
        columns={TransferDetailPostListTableColumns()}
        rowKey="id"
        search={false}
        params={{
          transferId,
        }}
        requestPage={async (params) => {
          if (!isEmpty(params?.transferId)) {
            return await queryTransferPostDetail({ ...params });
          }
          return { data: [], total: 0 };
        }}
        scroll={{ x: 1300 }}
        revalidateOnFocus={false}
      />
      <ConfirmModal {...confirmModalProps} onCancel={hideConfirmModal} />
    </PageContainer>
  );
};
