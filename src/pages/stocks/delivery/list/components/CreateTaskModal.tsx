import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form } from 'antd';
import { useEffect, useState } from 'react';
import { createDelivery, editDelivery, queryDeliveryDetail } from '../../services';
import { DeliveryEntity } from '../../types/Delivery.entity';
import { DeliveryType, DistributionMode, TargetType } from '../../types/delivery.enums';

interface CreateTaskModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editId?: string; // 编辑时的ID
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({
  visible,
  onClose,
  onSuccess,
  editId,
}) => {
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
  const [form] = Form.useForm<DeliveryEntity>();
  const [detail, setDetail] = useState<DeliveryEntity | null>(null);

  const loadDeliveryDetail = async () => {
    if (!editId) return;
    try {
      const detail = await queryDeliveryDetail({ id: editId });
      setDetail({
        ...detail,
        deliveryTargetType:
          detail.deliveryTargetType === TargetType.CUSTOMER
            ? ObjectType.Customer
            : TargetType.SUPPLIER,
      });
      form.setFieldsValue({
        ...detail,
        deliveryTargetType:
          detail.deliveryTargetType === TargetType.CUSTOMER
            ? ObjectType.Customer
            : TargetType.SUPPLIER,
      });
    } catch (error) {
      console.error('加载运单详情失败:', error);
    }
  };

  // 当弹框打开且有editId时，加载详情数据
  useEffect(() => {
    form.resetFields();
    if (visible && editId) {
      loadDeliveryDetail();
    }
  }, [visible, editId]);

  const handleFinish = async (values: DeliveryEntity) => {
    const params = {
      ...values,
      deliveryTargetType: values.deliveryTargetType === ObjectType.Suppler ? 1 : 2,
    };
    let result = false
    if (editId) {
      result = await editDelivery({
        ...params,
        id: editId,
      });
    } else {
      result = await createDelivery(params);
    }
    if (result) {
      onSuccess();
      onClose();
      return true;
    }
  };

  return (
    <ModalForm<DeliveryEntity>
      title={editId ? t('common.button.edit') : t('stocks.delivery.list.button.createTask')}
      width={600}
      form={form}
      open={visible}
      onOpenChange={(visible) => {
        if (!visible) {
          onClose();
        }
      }}
      onFinish={handleFinish}
      initialValues={{
        billType: DeliveryType.DELIVERY,
        distributionMode: DistributionMode.SELF_PICKUP,
      }}
    >
      <ProFormRadio.Group
        name="billType"
        label={t('stocks.delivery.list.column.taskType')}
        options={[
          { label: t('stocks.delivery.list.billType.delivery'), value: DeliveryType.DELIVERY },
          { label: t('stocks.delivery.list.billType.pickup'), value: DeliveryType.PICKUP },
        ]}
        disabled={Boolean(editId)}
      />
      <ProFormSelect
        name="warehouseId"
        label={t('stocks.delivery.list.column.warehouse')}
        showSearch
        required
        rules={[REQUIRED_RULES]}
        request={async () => {
          const data = await warehouseList({});
          return data?.warehouseSimpleRoList?.map(({ id, warehouseName }) => ({
            value: id,
            label: warehouseName,
          }));
        }}
        onChange={(value, option) => {
          form.setFieldsValue({
            warehouseName: option?.label,
          });
        }}
        disabled={Boolean(editId)}
      />
      <ProFormText name="warehouseName" hidden />
      <ProFormObject
        objects={[ObjectType.Customer, ObjectType.Suppler]}
        label={t('stocks.delivery.list.column.deliveryTarget')}
        form={form}
        fieldsName={{
          fieldType: 'deliveryTargetType',
          fieldName: 'deliveryTargetName',
          fieldId: 'deliveryTargetId',
        }}
        disabled={Boolean(editId)}
        required
        rules={[REQUIRED_RULES]}
        width={'100%'}
      />
      <ProFormText
        name="origBillNo"
        label={t('stocks.delivery.list.column.businessNo')}
        disabled={Boolean(editId) ? detail?.source === 0 : false}
      />

      <ProFormSelect
        name="deliveryManId"
        label={t('stocks.delivery.list.column.deliveryMan')}
        showSearch
        request={() => {
          return accountListQuerySimple({}).then((data) => {
            console.log(data);
            return data?.map(({ id, name }) => ({
              value: id,
              label: name,
            }));
          });
        }}
        onChange={(value, option) => {
          form.setFieldsValue({
            deliveryMan: option?.value,
          });
        }}
        disabled={Boolean(editId)}
      />
      <ProFormText name="deliveryMan" hidden />
      <ProFormDateTimePicker
        name="expectedArrTime"
        label={t('stocks.delivery.list.column.expectedArrivalTime')}
        width={'100%'}
      />
      <ProFormTextArea
        name="remark"
        label={t('stocks.delivery.list.column.description')}
        rules={[{ required: true }]}
      />
    </ModalForm>
  );
};

export default CreateTaskModal;
