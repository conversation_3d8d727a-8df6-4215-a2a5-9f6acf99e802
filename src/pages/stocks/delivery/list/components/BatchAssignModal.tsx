import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Flex, Form, message } from 'antd';
import { batchAssign } from '../../services';
import { BatchAssignModalProps } from '../type';

const BatchAssignModal = ({ visible, onClose, selectedRows, onSuccess }: BatchAssignModalProps) => {
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
  const [form] = Form.useForm();

  const handleFinish = async (values: { deliveryManId: string; deliveryMan: string }) => {
    try {
      await batchAssign({
        deliveryManId: values.deliveryManId,
        deliveryMan: values.deliveryMan || '',
        idList: selectedRows.map((row) => row.id),
      });
      message.success(t('stocks.delivery.list.message.assignSuccess'));
      onSuccess?.();
      onClose();
      form.resetFields();
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  };

  return (
    <ModalForm
      title={t('stocks.delivery.list.action.assign')}
      open={visible}
      onOpenChange={(visible) => {
        if (!visible) {
          onClose();
        }
      }}
      width={500}
      form={form}
      onFinish={handleFinish}
    >
      <ProFormSelect
        name="deliveryManId"
        label={t('stocks.delivery.list.column.deliveryMan')}
        rules={[{ required: true }]}
        showSearch
        request={async () => {
          const data = await accountListQuerySimple({});
          return (
            data?.map((item: any) => ({
              value: item.id,
              label: item.name,
            })) || []
          );
        }}
        onChange={(value, option: any) => {
          form.setFieldsValue({
            deliveryMan: option?.label || '',
          });
        }}
      />
      <ProFormText name="deliveryMan" hidden />
    </ModalForm>
  );
};

export default BatchAssignModal;
