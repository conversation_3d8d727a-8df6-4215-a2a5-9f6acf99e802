import Tag from '@/pages/finance/tag';
import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Popconfirm, Space } from 'antd';
import { DeliveryEntity } from '../../types/Delivery.entity';
import { DeliveryState, DeliveryStateMap } from '../../types/delivery.enums';

export const useDeliveryListColumns = (params: {
  handleAssign: (record: DeliveryEntity) => void;
  handleStartDelivery: (record: DeliveryEntity) => void;
  handleFinishDelivery: (record: DeliveryEntity) => void;
  handleEdit: (record: DeliveryEntity) => void;
  handleCancelDelivery: (record: DeliveryEntity) => void;
  currentUser: any;
  onDetail: (id: string) => void;
}): ProColumns<DeliveryEntity>[] => {
  const {
    handleAssign,
    handleStartDelivery,
    handleFinishDelivery,
    handleEdit,
    handleCancelDelivery,
    currentUser,
    onDetail,
  } = params;
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);

  return [
    {
      title: t('common.column.index'),
      dataIndex: 'index',
      valueType: 'index',
      width: 60,
    },
    {
      title: t('stocks.delivery.list.column.deliveryNo'),
      dataIndex: 'bizBillNo',
      width: 180,
      order: 1,
      render: (text, record) => {
        return (
          <Space>
            <a onClick={() => onDetail?.(record.id)}>{text}</a>
            {Boolean(record.isUrgent) && <Tag color="red">{t('delivery.tag.urgent')}</Tag>}
          </Space>
        );
      },
    },
    {
      title: t('stocks.delivery.list.column.businessNo'),
      dataIndex: 'origBillNo',
      width: 150,
      order: 2,
    },
    {
      title: t('stocks.delivery.list.column.taskType'),
      dataIndex: 'billTypeDesc',
      width: 100,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.warehouse'),
      dataIndex: 'warehouseName',
      width: 120,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 'responsive',
        showSearch: true,
      },
      formItemProps: {
        name: 'warehosueIdList',
      },
      request: async () => {
        const data = await warehouseList({});
        return data?.warehouseSimpleRoList?.map(({ id, warehouseName }) => ({
          value: id,
          label: warehouseName,
        }));
      },
      order: 10,
    },
    {
      title: t('stocks.delivery.list.column.status'),
      dataIndex: 'state',
      width: 100,
      valueType: 'select',
      valueEnum: DeliveryStateMap,
      order: 9,
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'stateList',
      },
    },
    {
      title: t('stocks.delivery.list.column.createTime'),
      dataIndex: 'createTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.createTime'),
      dataIndex: 'createTime',
      width: 160,
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            startCreateTime: value?.[0] ? `${value[0]} 00:00:00` : '',
            endCreateTime: value?.[1] ? `${value[1]} 23:59:59` : '',
          };
        },
      },
      hideInTable: true,
    },
    {
      title: t('stocks.delivery.list.column.expectedArrivalTime'),
      dataIndex: 'expectedArrTime',
      width: 160,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.deliveryMan'),
      dataIndex: 'deliveryMan',
      width: 120,
      valueType: 'select',
      formItemProps: {
        name: 'deliveryManId',
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      order: 8,
    },
    {
      title: t('stocks.delivery.list.column.contact'),
      dataIndex: 'contactName',
      order: 6,
      render: (_, record) => `${record.contactFirstName ?? ''} ${record.contactLastName ?? ''}`,
    },
    {
      title: t('stocks.delivery.list.column.contactAddress'),
      dataIndex: 'deliveryAddress',
      width: 200,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.remark'),
      dataIndex: 'remark',
      hideInSearch: true,
    },
    // {
    //   title: t('stocks.delivery.list.column.deliveryMethod'),
    //   dataIndex: 'distributionMode',
    //   hideInSearch: true,
    //   valueType: 'select',
    //   valueEnum: DistributionModeMap,
    // },
    {
      title: t('stocks.delivery.list.column.deliveryTarget'),
      dataIndex: 'deliveryTargetName',
      width: 120,
      order: 7,
    },
    {
      title: t('stocks.delivery.list.column.startTime'),
      dataIndex: 'beginTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.finishTime'),
      dataIndex: 'finishTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: t('common.column.operation'),
      key: 'operation',
      width: 200,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => {
        return (
          <Space>
            {record.state === DeliveryState.PENDING && (
              <a onClick={() => handleAssign(record)}>{t('stocks.delivery.list.action.assign')}</a>
            )}
            {record.state === DeliveryState.PICKED_UP &&
              currentUser.accountId === record.deliveryManId && (
                <a onClick={() => handleStartDelivery(record)}>
                  {t('stocks.delivery.list.action.start')}
                </a>
              )}
            {record.state === DeliveryState.IN_DELIVERY &&
              currentUser.accountId === record.deliveryManId && (
                <a onClick={() => handleFinishDelivery(record)}>
                  {t('stocks.delivery.list.action.finish')}
                </a>
              )}
            {[DeliveryState.PENDING, DeliveryState.PICKED_UP, DeliveryState.IN_DELIVERY].includes(
              record.state as DeliveryState,
            ) && <a onClick={() => handleEdit(record)}>{t('common.button.edit')}</a>}

            {[DeliveryState.PENDING, DeliveryState.PICKED_UP, DeliveryState.IN_DELIVERY].includes(
              record.state as DeliveryState,
            ) && (
                <Popconfirm
                  title={t('tip.confirm.action', { action: t('common.button.cancel') })}
                  onConfirm={() => handleCancelDelivery(record)}
                >
                  <a>{t('common.button.cancel')}</a>
                </Popconfirm>
              )}
          </Space>
        );
      },
    },
  ];
};
