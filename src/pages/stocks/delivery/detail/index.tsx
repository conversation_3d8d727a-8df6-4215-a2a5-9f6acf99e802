import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { PageContainer, ProCard, ProColumns, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Button, Flex, Image, Space, Spin, Tag } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { queryDeliveryDetail } from '../services';
import type { DeliveryDetailEntity, StockOutDetailRoList } from '../types/Delivery.entity';
import { DeliveryStateMap, DeliveryTypeMap, DistributionModeMap } from '../types/delivery.enums';

const DeliveryDetail: React.FC = () => {
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
  const searchParams = new URLSearchParams(window.location.search);
  const id = searchParams.get('id');

  const [detail, setDetail] = useState<DeliveryDetailEntity | null>(null);
  const [loading, setLoading] = useState(true);

  const loadDetail = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    try {
      const result = await queryDeliveryDetail({ id });
      setDetail(result);
    } catch (error) {
      console.error('Failed to load delivery detail:', error);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    loadDetail();
  }, [id, loadDetail]);

  const renderImages = () => {
    if (!detail?.images) return '-';

    const imageList = detail.images.split(',').filter((img) => img.trim());
    if (imageList.length === 0) return '-';

    return (
      <div className="grid grid-cols-3 gap-4">
        {imageList.map((image) => (
          <Image
            key={image}
            src={image}
            alt="delivery-image"
            className="w-full h-32 object-cover rounded"
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return <Spin spinning={loading} />;
  }

  const itemColumns: ProColumns<StockOutDetailRoList>[] = [
    {
      title: t('common.column.index'),
      dataIndex: 'index',
      valueType: 'index',
      width: 60,
    },
    {
      title: t('goods.search.table.itemSn'),
      dataIndex: 'itemSn',
    },
    {
      title: t('goods.search.table.itemName'),
      dataIndex: 'itemName',
      ellipsis: true,
    },
    {
      title: t('goods.search.table.oeNos'),
      dataIndex: 'oeNo',
    },
    {
      title: t('goods.search.table.brandPartNos'),
      dataIndex: 'brandPartNo',
    },
    {
      title: t('goods.search.table.brandName'),
      dataIndex: 'brandName',
    },
    {
      title: t('goods.search.table.categoryName'),
      dataIndex: 'categoryName',
    },
    {
      title: t('goods.search.table.unitName'),
      dataIndex: 'unitName',
    },
    {
      title: t('goods.search.table.quantity'),
      dataIndex: 'amount',
    },
  ];

  return (
    <PageContainer>
      <Spin spinning={loading}>
        <ProCard className="rounded-lg">
          <ProDescriptions
            title={
              <Space>
                <span>{detail?.bizBillNo}</span>
                {detail?.billType && (
                  <Tag color="orange">
                    {DeliveryTypeMap[detail?.billType as keyof typeof DeliveryTypeMap].text}
                  </Tag>
                )}
                {Boolean(detail?.isUrgent) && <Tag color="red">{t('delivery.tag.urgent')}</Tag>}
                {detail?.state && (
                  <Tag color="blue">
                    {DeliveryStateMap[detail?.state as keyof typeof DeliveryStateMap].text}
                  </Tag>
                )}
              </Space>
            }
            column={{
              sm: 1,
              md: 2,
              lg: 3,
              xl: 4,
            }}
            dataSource={detail}
            columns={[
              {
                title: t('stocks.delivery.list.column.businessNo'),
                dataIndex: 'origBillNo',
              },
              {
                title: t('stocks.delivery.list.column.deliveryMethod'),
                dataIndex: 'distributionMode',
                valueEnum: DistributionModeMap,
              },
              {
                title: t('stocks.delivery.list.column.warehouse'),
                dataIndex: 'warehouseName',
              },
              {
                title: t('stocks.delivery.list.column.deliveryMan'),
                dataIndex: 'deliveryMan',
              },
              {
                title: t('stocks.delivery.list.column.deliveryTarget'),
                dataIndex: 'deliveryTargetName',
              },
              {
                title: t('stocks.delivery.list.column.contact'),
                dataIndex: 'contactName',
              },
              {
                title: t('stocks.delivery.list.column.contactPhone'),
                dataIndex: 'contactPhone',
                render: (_, record) =>
                  `${record.contactFirstName ?? ''} ${record.contactLastName ?? ''}`,
              },
              {
                title: t('stocks.delivery.list.column.deliveryAddress'),
                dataIndex: 'deliveryAddress',
              },
              {
                title: t('stocks.delivery.list.column.createTime'),
                dataIndex: 'createTime',
              },
              {
                title: t('stocks.delivery.list.column.expectedArrivalTime'),
                dataIndex: 'expectedArrTime',
              },
              {
                title: t('stocks.delivery.list.column.startTime'),
                dataIndex: 'beginTime',
              },
              {
                title: t('stocks.delivery.list.column.finishTime'),
                dataIndex: 'finishTime',
              },
              {
                title: t('stocks.delivery.detail.deliveryImages'),
                dataIndex: 'deliveryImages',
                render: renderImages,
              },
              {
                title: t('stocks.delivery.list.column.remark'),
                dataIndex: 'remark',
              },
            ]}
          />
        </ProCard>
        <ProCard
          title={<SubTitle text={t('stocks.delivery.detail.stockOutDetail')} />}
          className="mt-4 rounded-lg"
        >
          <FunProTable<StockOutDetailRoList>
            rowKey="id"
            dataSource={detail?.detailList}
            columns={itemColumns}
            search={false}
            pagination={false}
            toolBarRender={false}
          />
        </ProCard>

        <Flex justify="center" className="mt-4 bg-white p-4">
          <Button onClick={() => history.push('/stocks/delivery/list')}>
            {t('common.button.back')}
          </Button>
        </Flex>
      </Spin>
    </PageContainer>
  );
};

export default DeliveryDetail;
