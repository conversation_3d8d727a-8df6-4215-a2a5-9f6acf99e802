import ConfirmModal from '@/components/ConfirmModal';
import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import MoneyText from '@/components/common/MoneyText';
import { useKeepAliveTabs } from '@/layouts/useKeepAliveTabs';
import CustomerDetailDrawerForm from '@/pages/customer/list/components/CustomerDetailDrawerForm';
import { getCstDetail } from '@/pages/customer/list/services';
import type { CustomerDetailDrawerFormType } from '@/pages/customer/list/types/CustomerDetailDrawerFormType';
import { CustomerEntity } from '@/pages/customer/list/types/CustomerEntity';
import SettleWayConfirmModal from '@/pages/sales/returns/list/components/SettleWayConfirmModal';
import { RefundOrderStatus } from '@/pages/sales/returns/list/types/refund.order.status';
import { ReturnType } from '@/pages/sales/returns/operation/types/return.type';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { FormattedMessage } from '@@/exports';
import { RightOutlined } from '@ant-design/icons';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Flex, GetProps, Image, Space, Tag } from 'antd';
import { defaultTo, includes, sum } from 'lodash';
import { useEffect, useState } from 'react';
import GoodsDetailColumns from '../list/config/GoodsDetailColumns';
import OperatorColumns from '../list/config/OperatorColumns';
import SettleColumns from '../list/config/SettleColumns';
import type { SettleConfirmModalType } from '../list/types/SettleConfirmModalType';
import {
  cancelOrder,
  confirmRefund,
  directIn,
  drawOrder,
  getAfterSaleDetail,
} from '../operation/services';
import type {
  AfterSaleOrderGoodsRo,
  AfterSaleOrderRo,
  AfterSaleOrderTimeRo,
  AfterSaleRefundDetailRo,
} from '../operation/types/ReturnsAfterSaleDetailEntity';

export default () => {
  const intl = useIntl();
  const searchParams = new URLSearchParams(window.location.search);

  const orderNo = searchParams.get('orderNo');
  const orderId = searchParams.get('orderId');

  // 商品总数
  const [totalCount, setTotalCount] = useState<number>();
  // 退货单详情
  const [returnOrderDetail, setReturnOrderDetail] = useState<AfterSaleOrderRo>();
  // 用户信息详情
  const [cstDetail, setCstDetail] = useState<CustomerEntity>();

  // 退货明细
  const [dataSourceCache, setDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);

  // 客户详情
  const [detailModalProps, setDetailModalProps] = useState<CustomerDetailDrawerFormType>({
    visible: false,
    recordId: '',
    onCancel: () => {
      setDetailModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: '',
      }));
    },
    title: intl.formatMessage({ id: 'sales.returns.detail.customerDetail' }),
  });

  const { closeTab } = useKeepAliveTabs();

  /** 查询退货详情*/
  const getDetail = async () => {
    if (orderId && orderNo) {
      const result = await getAfterSaleDetail({ orderId, orderNo });

      if (result?.main) {
        setReturnOrderDetail(result);
        getCstDetail({ cstId: result?.main?.cstId }).then((result) => {
          if (result) {
            setCstDetail(result);
          }
        });
      }
      if (result?.goods) {
        setTotalCount(sum(result?.goods.map((t) => t.refundNum)) ?? 0);
        setDataSourceCache(
          result?.goods.map((t) => ({ ...t, storeName: result.main.storeName })) ?? [],
        );
      }
    }
  };
  //   查询详情
  useEffect(() => {
    getDetail();
  }, [orderId]);

  /**
   * 作废
   */
  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });
  /**
   * 确认结算
   */
  const [confirmSettleModalProps, setConfirmSettleModalProps] = useState<SettleConfirmModalType>({
    visible: false,
  });
  /**
   * 关闭确认框
   */
  const onConfirmCancel = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };
  /**
   * 确认【作废】
   */
  const onCancelOrder = async () => {
    if (!orderId || !orderNo) return;
    const result = await cancelOrder({ orderId, orderNo });
    if (result) {
      getDetail();
      onConfirmCancel();
    }
  };
  /**
   * 确认【撤回】
   */
  const onDrawOrder = async () => {
    if (!orderId || !orderNo) return;
    const result = await drawOrder({ orderId, orderNo });
    if (result) {
      onConfirmCancel();
      getDetail();
    }
  };
  /**
   * 确认【一键入库】
   */
  const onDirectIn = async () => {
    if (!orderId || !orderNo) return;
    const result = await directIn({ orderId, orderNo });
    if (result) {
      setTimeout(() => {
        getDetail();
        onConfirmCancel();
      }, 250);
    }
  };
  /**
   * 关闭【确认结算】
   */
  const onSettleCancel = async () => {
    setConfirmSettleModalProps((preProps) => ({
      ...preProps,
      orderId: '',
      orderNo: '',
      cstId: '',
      storeId: '',
      orderAmount: 0,
      visible: false,
    }));
  };
  /**
   * 确认【确认结算】
   */
  const onConfirmPay = async () => {
    if (!orderId || !orderNo) return;
    const result = await confirmRefund({ orderId, orderNo });
    if (result) {
      getDetail();
      onSettleCancel();
    }
  };

  /**
   * 查看详情
   * @param id
   */
  const openDetailModal = (id: string) => {
    setDetailModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      recordId: id,
    }));
  };

  // 单据状态颜色
  const orderStatusColor = () => {
    if (
      [RefundOrderStatus.FINISH, RefundOrderStatus.HAS_IN].includes(
        returnOrderDetail?.status?.orderStatus!,
      )
    ) {
      return 'green';
    }
    if (RefundOrderStatus.CANCEL === returnOrderDetail?.status?.orderStatus) {
      return 'gray';
    }
    if (
      ![RefundOrderStatus.HAS_IN, RefundOrderStatus.CANCEL, RefundOrderStatus.FINISH].includes(
        returnOrderDetail?.status?.orderStatus!,
      )
    ) {
      return 'red';
    }
    return '';
  };

  // 结算状态颜色
  const refundStatusColor = () => {
    if (returnOrderDetail?.status?.refundStatus == RefundOrderStatus.FINISH) {
      return 'green';
    } else {
      return 'red';
    }
  };
  // 草稿或者已作废只展示单据状态不展示结算状态
  const hiddenRefundStatus = !includes(
    [RefundOrderStatus.DRAFT, RefundOrderStatus.CANCEL],
    returnOrderDetail?.status?.orderStatus,
  );
  return (
    <PageContainer>
      <ProCard className="mb-4">
        <Flex justify="space-between">
          <Space>
            <span className="text-[20px] font-semibold text-[#000000D9]">{orderNo}</span>
            <div>
              {/* 单据状态：已完成绿色 已作废灰色 其余状态红色 */}
              <Tag color={orderStatusColor()}>{returnOrderDetail?.status?.orderStatusName}</Tag>
              {/* 结算状态：已结算绿色未结算红色 */}
              {hiddenRefundStatus && (
                <Tag color={refundStatusColor()}>{returnOrderDetail?.status?.refundStatusName}</Tag>
              )}
              {returnOrderDetail?.main?.tagIds?.includes(ReturnType.Good) && (
                <Tag color="green">
                  <FormattedMessage id={'sales.returns.operation.returnType.good'} />
                </Tag>
              )}
              {returnOrderDetail?.main?.tagIds?.includes(ReturnType.Broken) && (
                <Tag color="red">
                  <FormattedMessage id={'sales.returns.operation.returnType.broken'} />
                </Tag>
              )}
            </div>
          </Space>
          <Space>
            <AuthButton
              authority="editSaleReturn"
              visible={returnOrderDetail?.status?.orderStatus == RefundOrderStatus.DRAFT}
              onClick={() => {
                closeTab();
                history.push(`/sales/returns/operation?orderId=${orderId}&orderNo=${orderNo}`);
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.detail.edit' })}
            </AuthButton>
            <AuthButton
              authority="withdrawSaleReturn"
              visible={returnOrderDetail?.status?.orderStatus == RefundOrderStatus.TO_IN}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.withdraw' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmWithdraw' }),
                  onOk: onDrawOrder,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.withdraw' })}
            </AuthButton>
            <AuthButton
              authority="deleteSaleReturn"
              visible={[RefundOrderStatus.DRAFT, RefundOrderStatus.TO_IN].includes(
                returnOrderDetail?.status?.orderStatus!,
              )}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.void' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmVoid' }),
                  onOk: onCancelOrder,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.void' })}
            </AuthButton>
            <AuthButton
              authority="saleReturnPrint"
              onClick={() =>
                window.open(
                  `/print?orderNo=${orderNo}&orderId=${orderId}&printType=${PrintType.salesReturnOrder}`,
                )
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.print' })}
            </AuthButton>
            <AuthButton
              authority="saleReturnInWareHouse"
              type="primary"
              visible={returnOrderDetail?.status?.orderStatus === RefundOrderStatus.TO_IN}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.oneClickInbound' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmOneClickInbound' }),
                  onOk: onDirectIn,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.oneClickInbound' })}
            </AuthButton>
            <AuthButton
              authority="orderReturnSettlement"
              type="primary"
              visible={
                returnOrderDetail?.status?.refundStatus === RefundOrderStatus.DRAFT &&
                (returnOrderDetail?.status?.orderStatus === RefundOrderStatus.TO_IN ||
                  returnOrderDetail?.status?.orderStatus === RefundOrderStatus.HAS_IN)
              }
              onClick={() => {
                setConfirmSettleModalProps((preProps) => ({
                  ...preProps,
                  visible: true,
                  onOk: () => getDetail(),
                }));
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.detail.confirmSettlement' })}
            </AuthButton>
          </Space>
        </Flex>

        <ProDescriptions
          className="mt-5"
          column={4}
          dataSource={{
            cstName: cstDetail?.base?.cstName,
            orderStatusName: returnOrderDetail?.status?.orderStatusName,
            orderAmount: returnOrderDetail?.main.orderAmount ?? 0,
            refundTypeName: returnOrderDetail?.refunds?.[0]?.refundTypeName,
            storeName: returnOrderDetail?.main?.storeName,
            backWarehouseName: returnOrderDetail?.main?.backWarehouseName,
            belongingStoreName: returnOrderDetail?.main?.belongingStoreName,
            saleCompanyName: returnOrderDetail?.main?.saleCompanyName,
            orderCreateTime: returnOrderDetail?.main?.orderCreateTime,
            salesmanName: returnOrderDetail?.main?.salesmanName,
            currency: returnOrderDetail?.main?.currency,
            exchangeRate: returnOrderDetail?.main?.exchangeRate,
            gstExcluded: returnOrderDetail?.main?.gstExcluded,
            returnRemark: returnOrderDetail?.notes?.find((item) => item.noteType === 1)?.noteDetail,
            innerRemark: returnOrderDetail?.notes?.find((item) => item.noteType === 2)?.noteDetail,
            referenceNo: returnOrderDetail?.main?.referenceNo,
            images: returnOrderDetail?.images,
          }}
          columns={[
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.customerName' }),
              dataIndex: 'cstName',
              renderText: (text) => (
                <span className="flex">
                  {text}
                  <RightOutlined
                    className="cursor-pointer"
                    title={intl.formatMessage({ id: 'sales.returns.detail.viewCustomerDetail' })}
                    onClick={() => {
                      const cstId = cstDetail?.base?.id;
                      if (cstId) {
                        openDetailModal(cstId);
                      }
                    }}
                  />
                </span>
              ),
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.orderStatus' }),
              dataIndex: 'orderStatusName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.refundAmount' }),
              dataIndex: 'orderAmount',
              valueType: 'money',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.settlementMethod' }),
              dataIndex: 'refundTypeName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.returnStore' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.receiveWarehouse' }),
              dataIndex: 'backWarehouseName',
            },
            {
              title: intl.formatMessage({ id: 'sales.order.detail.belongingStoreName' }),
              key: 'belongingStoreName',
              dataIndex: 'belongingStoreName',
            },
            {
              title: '销售主体',
              key: 'saleCompanyName',
              dataIndex: 'saleCompanyName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.orderTime' }),
              dataIndex: 'orderCreateTime',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.creator' }),
              dataIndex: 'salesmanName',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.currency' }),
              key: 'currency',
              dataIndex: 'currency',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.rate' }),
              key: 'exchangeRate',
              dataIndex: 'exchangeRate',
            },
            {
              title: 'GST Excluded',
              key: 'gstExcluded',
              dataIndex: 'gstExcluded',
              renderText: (text) => (text ? 'Yes' : 'No'),
            },
            {
              title: 'Reference No.',
              key: 'referenceNo',
              dataIndex: 'referenceNo',
            },
            {
              title: intl.formatMessage({ id: 'sales.order.edit.returnRemark' }),
              key: 'returnRemark',
              dataIndex: 'returnRemark',
            },
            {
              title: intl.formatMessage({ id: 'sales.order.edit.innerRemark' }),
              key: 'innerRemark',
              dataIndex: 'innerRemark',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.returnImage' }),
              key: 'images',
              dataIndex: 'images',
              render: (_, record) => {
                return (
                  <Image.PreviewGroup>
                    {record?.images?.map((item) => (
                      <div className="mr-1">
                        <Image width={60} src={item.url} />
                      </div>
                    ))}
                  </Image.PreviewGroup>
                );
              },
            },
          ]}
        />
      </ProCard>
      <FunProTable<AfterSaleOrderGoodsRo>
        headerTitle={
          <LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.goodsDetail' })} />
        }
        search={false}
        pagination={false}
        scroll={{ x: 800 }}
        dataSource={dataSourceCache}
        options={false}
        columns={GoodsDetailColumns()}
      />
      <ProCard bordered>
        <Flex gap={40} key="summary" className="flex justify-between items-center">
          <div className="flex gap-6">
            <span className="text-[16px] font-semibold text-[#000000D9]">
              {intl.formatMessage({ id: 'sales.returns.detail.totalQuantity' })}：
              {defaultTo(totalCount, '0')}
            </span>
            <span className="text-[16px] font-semibold text-[#000000D9]">
              {intl.formatMessage({ id: 'sales.returns.operation.totalGoodsAmount' })}：
              <MoneyText text={defaultTo(returnOrderDetail?.main?.orderAmount, '0')} />
            </span>
            <span className="text-[16px] font-semibold text-[#000000D9]">
              GST：
              <MoneyText text={defaultTo(returnOrderDetail?.main?.totalTaxationAmount, '0')} />
            </span>
            <span className="text-[16px] font-semibold text-[#000000D9]">
              {intl.formatMessage({ id: 'sales.order.edit.adjustAmount' })}：
              <MoneyText text={defaultTo(returnOrderDetail?.main?.adjustment, '0')} />
            </span>
          </div>
          <div>
            <span className="flex flex-row items-center">
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {intl.formatMessage({ id: 'sales.returns.detail.totalReturnAmount' })}：
              </span>
              <span className="text-[24px] font-medium text-[#F83431]">
                <MoneyText text={defaultTo(returnOrderDetail?.main?.refundAmount, 0).toFixed(2)} />
              </span>
            </span>
          </div>
        </Flex>
      </ProCard>
      <FunProTable<AfterSaleRefundDetailRo, any>
        headerTitle={
          <LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.settlementRecord' })} />
        }
        search={false}
        pagination={false}
        className="mt-4"
        scroll={{ x: 'max-content' }}
        dataSource={returnOrderDetail?.refundDetails ?? []}
        options={false}
        columns={SettleColumns()}
      />
      <FunProTable<AfterSaleOrderTimeRo, any>
        headerTitle={
          <LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.operationRecord' })} />
        }
        search={false}
        pagination={false}
        className="mt-4"
        scroll={{ x: 'max-content' }}
        dataSource={returnOrderDetail?.times}
        options={false}
        columns={OperatorColumns()}
      />
      <SettleWayConfirmModal
        {...confirmSettleModalProps}
        onCancel={onSettleCancel}
        cstDetail={cstDetail}
        returnOrderDetail={returnOrderDetail}
        onOk={onConfirmPay}
        onChange={getDetail}
      />
      <ConfirmModal {...confirmModalProps} onCancel={onConfirmCancel} />
      <CustomerDetailDrawerForm {...detailModalProps} />
    </PageContainer>
  );
};
