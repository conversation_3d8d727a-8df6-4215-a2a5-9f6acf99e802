import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';
import { getCstList } from '@/pages/customer/list/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { updateOrderMain } from '@/pages/sales/order/edit/services';
import { SaleOrgEntity } from '@/pages/system/store/types/sale.org.entity';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProForm, ProFormInstance, ProFormSelect } from '@ant-design/pro-components';
import { ProFormText } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import type { MutableRefObject } from 'react';
import { querySaleOrgList } from '@/pages/system/store/services';
import type { BaseOptionsType } from '@/types/BaseOptionsType';

export interface CstFormProps {
  cstFormRef: MutableRefObject<ProFormInstance>;
  orderId?: string;
  orderNo?: string;
  setWarehouseId: (id: string) => void;
  setStoreId: (id: string) => void;
  isMultiCurrency?: boolean;
  warehouseOptions: BaseOptionsType<string, string>[];
}

const CstForm = (props: CstFormProps) => {
  const intl = useIntl();
  const { cstFormRef, orderId, orderNo, setStoreId, isMultiCurrency = false } = props;
  return (
    <ProForm.Group>
      <ProFormSelect
        debounceTime={300}
        label={intl.formatMessage({ id: 'sales.order.edit.customerName' })}
        placeholder={intl.formatMessage({ id: 'sales.order.edit.selectCustomer' })}
        name="cstId"
        allowClear={false}
        showSearch={true}
        rules={[REQUIRED_RULES]}
        fieldProps={{
          filterOption: false,
        }}
        disabled={Boolean(orderNo)}
        onChange={(value, item) => {
          cstFormRef?.current?.setFieldValue?.('cstName', item.title);
        }}
        request={(query) =>
          getCstList({ keyword: query.keyWords, cstStatus: 0, limit: 99999 }).then((result) => {
            // @ts-ignore
            const list = result.map((item) => ({ label: item.cstName, value: item.cstId }));
            return list;
          })
        }
        colProps={{
          span: 6,
        }}
      />
      <ProFormText hidden name="cstName" />
      <ProFormSelect
        label={intl.formatMessage({ id: 'sales.order.edit.salesStore' })}
        placeholder={intl.formatMessage({ id: 'sales.order.edit.selectSalesStore' })}
        name="storeId"
        rules={[REQUIRED_RULES]}
        allowClear={false}
        disabled={Boolean(orderNo)}
        onChange={(value, item) => cstFormRef?.current?.setFieldsValue?.({ storeName: item.label })}
        request={(query) =>
          queryStoreByAccount({ status: 1 }).then((result) => {
            const list = result?.map((item) => ({ label: item.name, value: item.id })) ?? [];
            // 查看是否有默认值
            if (!orderNo && !cstFormRef.current.getFieldValue('storeId')) {
              let defaultItem;
              defaultItem = result?.find((item) => item.type === '0');
              if (!defaultItem) {
                defaultItem = result?.[0];
              }
              if (defaultItem) {
                cstFormRef?.current?.setFieldsValue?.({
                  storeId: defaultItem.id,
                  storeName: defaultItem.name,
                });
                setStoreId(defaultItem.id);
              }
            }
            return list;
          })
        }
        colProps={{
          span: 6,
        }}
      />
      <ProFormText hidden name="storeName" />
      <ProFormText hidden name="saleCompanyName" />
      <ProFormSelect
        label="销售主体"
        name="saleCompanyId"
        rules={[REQUIRED_RULES]}
        allowClear={false}
        dependencies={['storeId', 'cstId']}
        onChange={(value, item) => {
          cstFormRef?.current?.setFieldsValue?.({ saleCompanyName: item.label });
          if (orderNo) {
            updateOrderMain({
              orderId: orderId,
              saleCompanyName: item.label,
              saleCompanyId: item.value,
            });
          }
        }}
        // @ts-ignore
        request={(query) => {
          if (!query.storeId || !query.cstId) {
            return [];
          }
          return querySaleOrgList({ storeId: query.storeId, cstId: query.cstId }).then((result) => {
            const list: any[] =
              // @ts-ignore
              result?.map((item: SaleOrgEntity) => ({
                label: item.saleOrgName,
                value: item.saleOrgId,
                isDefault: item.isDefault,
              })) ?? [];
            // 查看是否有默认值
            if (!orderNo && !cstFormRef.current.getFieldValue('saleCompanyId')) {
              let defaultItem = list.find((item) => item.isDefault);
              if (defaultItem) {
                if (!defaultItem) {
                  defaultItem = list?.[0];
                }
                if (defaultItem) {
                  cstFormRef?.current?.setFieldsValue?.({
                    saleCompanyId: defaultItem.value,
                    saleCompanyName: defaultItem.label,
                  });
                }
              }
            }
            return list;
          });
        }}
        colProps={{
          span: 6,
        }}
      />
      <ProFormText hidden name="saleCompanyName" />
      <ProFormSelect
        label={intl.formatMessage({ id: 'sales.order.edit.deliveryWarehouse' })}
        placeholder={intl.formatMessage({ id: 'sales.order.edit.selectDeliveryWarehouse' })}
        name="warehouseId"
        rules={[REQUIRED_RULES]}
        allowClear={false}
        dependencies={['storeId']}
        onChange={(value, item) => {
          cstFormRef?.current?.setFieldsValue?.({ warehouseName: item.label });
          if (orderNo) {
            updateOrderMain({
              orderId: orderId,
              warehouseName: item.label,
              warehouseId: item.value,
            });
          }
        }}
        options={props.warehouseOptions}
        colProps={{
          span: 6,
        }}
      />
      <ProFormText
        name="referenceNo"
        label="Reference No."
        colProps={{
          span: 6,
        }}
      />
      {isMultiCurrency && (
        <ProFormCurrency
          disabled={Boolean(orderNo)}
          // @ts-ignore
          colProps={{
            span: 6,
          }}
        />
      )}
      <ProFormText hidden name="warehouseName" />
    </ProForm.Group>
  );
};

export default CstForm;
