import SubTitle from '@/components/common/SubTitle';
import {
  OrderGoodsROList,
  OrderListItemEntity,
} from '@/pages/sales/order/list/types/order.list.item.entity';
import { useModel } from '@@/exports';
import { EditableFormInstance, EditableProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
import _ from 'lodash';
import React, { useRef, useState } from 'react';
import SalesGoodsColumns from '../../config/SalesGoodsColumns';
import GoodsDetailDrawer, { GoodsDetailDrawerProps } from '@/components/GoodsDetailDrawer';
import GoodsActivityDrawer, { GoodsActivityDrawerProps } from '@/components/GoodsActivityDrawer';

export interface HandleUpdatePriceOrNumberProps {
  id: string;
  number: number;
  price: number;
}

export interface SalesListProps {
  orderDetail?: OrderListItemEntity;
  handleDeleteGoodItem: (id: string) => void;
  handleUpdatePriceOrNumber: (data: HandleUpdatePriceOrNumberProps) => void;
  cstId?: string;
  storeId?: string;
  warehouseId?: string;
  orderNo?: string;
}

const SalesList = (props: SalesListProps) => {
  const { storeId, cstId } = props;
  const intl = useIntl();
  const formRef = useRef<EditableFormInstance>();
  const { handleUpdatePriceOrNumber, handleDeleteGoodItem, orderDetail, warehouseId, orderNo } =
    props;
  const dataSource = orderDetail?.orderGoodsROList ?? [];

  // 查看商品
  const [goodsDrawer, setGoodsDrawer] = useState<GoodsDetailDrawerProps>({
    visible: false,
  });

  // 查看活动信息
  const [activityDrawer, setActivityDrawer] = useState<GoodsActivityDrawerProps>({
    visible: false,
  });

  const updateFn = useDebounceFn(
    (data) => {
      handleUpdate(data);
    },
    { wait: 500 },
  );

  const handleUpdate = (id: string) => {
    const formData = formRef.current?.getRowData?.(id);
    console.log('formData', formData);
    handleUpdatePriceOrNumber?.({
      id,
      number: formData.saleNum || 1,
      price: formData.unitPriceYuan || 0.01,
    });
  };

  /**
   * 计算datasource中的毛利率
   */
  const getValue = () => {
    return dataSource?.map((item) => ({
      ...item,
      grossMargin: _.round(
        _.multiply(item.saleNum ?? 0, _.subtract(item.unitPriceYuan ?? 0, item.costPriceYuan ?? 0)),
        2,
      ),
    }));
  };

  return (
    <>
      <EditableProTable<OrderGoodsROList, any>
        editableFormRef={formRef}
        pagination={false}
        scroll={{ x: 1300, y: 'max-content' }}
        ghost={true}
        options={{ setting: true, fullScreen: false, density: false, reload: false }}
        headerTitle={
          <SubTitle
            text={
              <span>
                {intl.formatMessage({ id: 'sales.order.edit.salesDetail' })}
                {orderDetail?.orders?.orderNo && (
                  <span className="font-normal text-base ml-8">
                    {intl.formatMessage({ id: 'sales.order.edit.salesOrderNo' })}:{' '}
                    {orderDetail?.orders?.orderNo}
                  </span>
                )}
                {orderDetail?.orderStatus?.orderStatusName && (
                  <span className="font-normal text-base ml-8">
                    {intl.formatMessage({ id: 'sales.order.edit.salesStatus' })}:{' '}
                    {orderDetail?.orderStatus?.orderStatusName}
                  </span>
                )}
              </span>
            }
          />
        }
        search={false}
        recordCreatorProps={false}
        rowKey="id"
        editable={{
          editableKeys: dataSource.map((item) => item.id),
        }}
        value={getValue()}
        columns={SalesGoodsColumns({
          handleUpdate: updateFn.run,
          handleDeleteGoodItem,
          setGoodsDrawer,
          intl,
          cstId,
          storeId,
          warehouseId,
          orderNo,
          setActivityDrawer,
        })}
      />
      <GoodsDetailDrawer {...goodsDrawer} onClose={() => setGoodsDrawer({ visible: false })} />
      <GoodsActivityDrawer
        {...activityDrawer}
        onClose={() => setActivityDrawer({ visible: false })}
      />
    </>
  );
};

export default SalesList;
