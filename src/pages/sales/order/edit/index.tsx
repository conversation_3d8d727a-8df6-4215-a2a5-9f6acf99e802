import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import PaymentForm, { ADVANCE_ACCOUNT, getAccountList } from '@/components/PaymentForm';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';
import { PayKind } from '@/components/PaymentForm/types/PayKind';
import { getCstDetail } from '@/pages/customer/list/services';
import { CustomerEntity } from '@/pages/customer/list/types/CustomerEntity';
import type { Address } from '@/pages/customer/list/types/CustomerSaveEntity';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import AdjustAmountForm from '@/pages/sales/order/edit/compoments/AdjustAmountForm';
import CstDetail from '@/pages/sales/order/edit/compoments/CstDetail';
import DeliveryForm from '@/pages/sales/order/edit/compoments/DeliveryForm';
import DiscountForm from '@/pages/sales/order/edit/compoments/DiscountForm';
import { DeliveryMethod } from '@/pages/sales/order/edit/types/DeliveryMethod';
import { DiscountType } from '@/pages/sales/order/edit/types/DiscountType';
import { AdjustAmountType } from '@/pages/sales/order/edit/types/adjust.amount.type';
import type { ConfirmPayRequest } from '@/pages/sales/order/edit/types/confirm.pay.request';
import { CouponEntity } from '@/pages/sales/order/edit/types/coupon.entity';
import { UpdateAdjustmentRequest } from '@/pages/sales/order/edit/types/update.adjustment.request';
import type { UpdateOrderDiscountRequest } from '@/pages/sales/order/edit/types/update.order.discount.request';
import { RemarkType } from '@/pages/sales/order/edit/types/update.order.remark.request';
import { UpdateOrderTagRequest } from '@/pages/sales/order/edit/types/update.order.tag.request';
import type { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import withKeepAlive from '@/wrappers/withKeepAlive';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormItem,
  ProFormSwitch,
} from '@ant-design/pro-components';
import { ProFormTextArea } from '@ant-design/pro-form';
import { history, useIntl } from '@umijs/max';
import { useDebounceEffect, useDebounceFn } from 'ahooks';
import { App, Col, ConfigProvider, message, Row } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CstForm from './compoments/CstForm';
import type { HandleUpdatePriceOrNumberProps } from './compoments/SalesList';
import SalesList from './compoments/SalesList';
import SumBar from './compoments/SumBar';
import {
  createDraftOrder,
  getAvailableCouponListForEc,
  getOrderByOrderNoForDbReturnSelected,
  updateAdjustment,
  updateExtendExpense,
  updateOrderAddress,
  updateOrderDeliveryInfo,
  updateOrderDiscount,
  updateOrderItem,
  updateOrderMain,
  updateOrderPayKind,
  updateOrderRemark,
  updateOrderTag,
} from './services';
import type { CreateDraftOrderRequest } from './types/create.draft.order.request';
import { OrderChannelCode } from './types/order.channel.code';
import { OprateType } from './types/update.order.item.request';
import ProFormChipTagInput from '@/components/ProFormItem/ProFormChipTagInput';
import type { BaseOptionsType } from '@/types/BaseOptionsType';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { AdjustType } from '@/pages/finance/receive/types/ReceivedConfirmation';

const SalesOrderEdit = () => {
  // const searchParams = new URLSearchParams(window.location.search);
  const searchParams = new URLSearchParams(window.location.search);
  const { modal } = App.useApp();
  const intl = useIntl();
  // 客户信息表单
  const cstFormRef = useRef<ProFormInstance>();
  //  客户信息表单变化监听使用防抖
  const cstFormChange = useDebounceFn(
    (changedValues: any, allValues: any) => handleCstFormDataChange(changedValues),
    {
      wait: 1000,
    },
  );
  // 客户信息详情
  const [cstDetail, setCstDetail] = useState<CustomerEntity>();
  const [accountList, setAccountList] = useState<any[]>([]);
  // 订单详情
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  // 底部的额外表单
  const extraFormRef = useRef<ProFormInstance>();
  // 底部表单值变化监听使用防抖
  const extraFormChange = useDebounceFn(
    (changedValues: any, allValues: any) => handleExtraFormDataChange(changedValues, allValues),
    {
      wait: 1000,
    },
  );
  const [warehouseId, setWarehouseId] = useState<string>();
  const [storeId, setStoreId] = useState<string>();
  // 可用优惠券列表
  const [couponList, setCouponList] = useState<CouponEntity[]>();
  // 发货仓库列表
  const [warehouseOptions, setWarehouseOptions] = useState<BaseOptionsType<string, string>[]>([]);

  const orderNo = searchParams.get('orderNo');

  // 设置默认值
  const setDefaultData = () => {
    extraFormRef.current?.setFieldsValue({
      discountType: DiscountType.NONE,
      deliveryMethod: DeliveryMethod.MERCHANT_DELIVERY,
    });
  };

  useActivate(() => {
    queryDetail();
  });

  // 初始化
  useEffect(() => {
    setDefaultData();
    queryDetail();

    return () => {
      setOrderDetail(undefined);
      cstFormRef.current?.resetFields();
      extraFormRef?.current?.resetFields();
      setCstDetail(undefined);
    };
  }, [orderNo]);

  useEffect(() => {
    if (storeId) {
      warehouseList({
        state: YesNoStatus.YES,
        storeIdList: [storeId],
      }).then((result) => {
        const list =
          result?.warehouseStoreRelationRoList?.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          })) ?? [];
        // 查看是否有默认值
        if (!orderNo) {
          let defaultItem;
          defaultItem = result?.warehouseStoreRelationRoList?.find((item) => item.isDefault);
          if (!defaultItem) {
            defaultItem = result?.warehouseStoreRelationRoList?.[0];
          }
          if (defaultItem) {
            cstFormRef?.current?.setFieldsValue?.({
              warehouseId: defaultItem.warehouseId,
              warehouseName: defaultItem.warehouseName,
            });
            setWarehouseId(defaultItem.warehouseId!);
          }
        }
        setWarehouseOptions(list);
      });
    }
  }, [storeId]);

  useDebounceEffect(
    () => {
      if (cstDetail && orderDetail) {
        queryMemberAccountPage({
          pageSize: 1000,
          currency: orderDetail?.orders?.currency,
          saleOrgId: orderDetail?.orders?.saleCompanyId,
        }).then((result) => {
          setAccountList(
            // @ts-ignore
            getAccountList({
              // @ts-ignore
              list: result.data,
              cstDetail,
              currency: orderDetail?.orders?.currency,
            }).list,
          );
        });
      }
      return () => {
        setAccountList([]);
      };
    },
    [cstDetail, orderDetail],
    { wait: 50 },
  );

  // 如果客户信息变化
  useEffect(() => {
    if (cstDetail && !orderNo) {
      // 获取默认的地址作为商家配送地址
      cstDetail.addresses?.forEach((item, index) => {
        if (item.isDefault) {
          extraFormRef.current?.setFieldsValue({
            addressId: item.id,
          });
        }
      });
      // 结算方式设置默认值
      extraFormRef.current?.setFieldsValue({
        payKind: cstDetail.settle?.credit ? PayKind.Credit : PayKind.Cash,
      });
    }
  }, [cstDetail]);

  /**
   * 查询订单信息
   */
  const queryDetail = () => {
    if (orderNo) {
      getOrderByOrderNoForDbReturnSelected(orderNo).then((result) => {
        if (result) {
          setOrderDetail(result);
        }
      });
    }
  };

  /**
   * 查询可用优惠券列表
   */
  const queryCouponList = () => {
    if (orderDetail?.orders?.currency !== 'AUD') {
      return;
    }
    getAvailableCouponListForEc({
      cstId: orderDetail?.orders?.cstId,
      items: orderDetail?.orderGoodsROList?.map((item) => ({
        brandId: item.itemId,
        categoryId: item.categoryId,
        itemId: item.itemId,
        saleAmountTax: item.unitPriceTaxYuan * item.saleNum!,
      })),
    }).then((res) => {
      // @ts-ignore
      setCouponList(res?.filter((item) => item.canUse));
    });
  };

  /**
   * 回填订单信息到表单
   */
  useEffect(() => {
    if (orderDetail) {
      cstFormRef.current?.setFieldsValue({
        cstId: orderDetail?.orders?.cstId,
        storeId: orderDetail?.orders?.storeId,
        warehouseId: orderDetail?.orderFixedDistributionList?.[0]?.warehouseId,
        currency: orderDetail?.orders?.currency,
        rate: orderDetail?.orders?.exchangeRate,
        referenceNo: orderDetail?.orders?.referenceNo,
        saleCompanyId: orderDetail?.orders?.saleCompanyId,
        saleCompanyName: orderDetail?.orders?.saleCompanyName,
      });
      setWarehouseId(orderDetail?.orderFixedDistributionList?.[0]?.warehouseId);
      setStoreId(orderDetail?.orders?.storeId);
      extraFormRef.current?.setFieldsValue({
        saleRemark: orderDetail?.orderNoteList?.find(
          (item) => item.noteType === RemarkType.StoreToCustomer,
        )?.noteDetail,
        innerRemark: orderDetail?.orderNoteList?.find(
          (item) => item.noteType === RemarkType.StoreToInner,
        )?.noteDetail,
        vehicleNos: orderDetail?.orderNoteList?.find(
          (item) => item.noteType === RemarkType.VehicleNos,
        )?.noteDetail,
        isPrint: orderDetail?.orderNoteList?.find(
          (item) => item.noteType === RemarkType.StoreToCustomer,
        )?.isPrint,
        estimatedDeliveryTime: orderDetail?.orderFixedDistributionList?.[0]?.estimatedDeliveryTime,
        discountType: orderDetail?.orderFixedDiscountList?.[0]?.discountType,
        discountMoney: orderDetail?.orderFixedDiscountList?.[0]?.discountMoneyYuan,
        deliveryAmount: orderDetail?.orderPrice?.deliveryAmountYuan,
        deliveryMethod: orderDetail?.orderFixedDistributionList?.[0]?.distributionMode,
        payKind: orderDetail?.orderPayDetailList?.[0]?.payKind,
        adjustmentType: orderDetail?.orderPrice?.adjustmentType,
        adjustment: orderDetail?.orderPrice?.adjustmentYuan
          ? Math.abs(orderDetail?.orderPrice?.adjustmentYuan)
          : undefined,
        adjustmentReason: orderDetail?.orderPrice?.adjustmentReason,
        operatorType: orderDetail?.orders?.orderTagList?.includes(2),
      });
      if (orderDetail?.orderFixedDiscountList?.[0]?.discountDesc !== 'null') {
        extraFormRef.current?.setFieldsValue({
          // @ts-ignore
          discountRate: 100 - orderDetail?.orderFixedDiscountList?.[0]?.discountDesc * 10,
        });
      }
      if (orderDetail?.orderFixedDiscountList?.[0]?.discountType === DiscountType.COUPON) {
        extraFormRef.current?.setFieldsValue({
          couponId: Number(orderDetail?.orderFixedDiscountList?.[0]?.discountItemId),
        });
      }
      if (orderDetail?.orderPayDetailList?.[0]?.payKind === PayKind.Cash) {
        const payDetailList: any[] = [];
        orderDetail?.orderPayDetailList?.forEach((item) => {
          if (item.payChannel === PayChannel.ADVANCE) {
            payDetailList.push({
              payeeAcount: ADVANCE_ACCOUNT,
              payAmount: item.payAmountYuan,
            });
          } else if (
            typeof item.payAmountYuan !== 'undefined' &&
            typeof item.payeeAccount !== 'undefined'
          ) {
            payDetailList.push({
              payeeAcount: item.payeeAccount,
              payAmount: item.payAmountYuan,
            });
          }
        });
        if (payDetailList.length) {
          extraFormRef.current?.setFieldsValue({
            payDetailList: payDetailList,
          });
        }
      }
      if (orderDetail?.orderFixedDiscountList?.length === 0) {
        extraFormRef.current?.setFieldsValue({
          discountType: DiscountType.NONE,
        });
      }
      setTimeout(() => {
        // 因为初始化时某些表单没有创建，所以需要延迟赋值
        extraFormRef.current?.setFieldsValue({
          addressId: orderDetail?.orderFixedAddressList?.[0]?.addressId,
          logisticsCompanyName: orderDetail?.orderFixedDistributionList?.[0]?.logisticsCompanyName,
          logisticsNo: orderDetail?.orderFixedDistributionList?.[0]?.logisticsNo,
          deliveryMan: orderDetail?.orderFixedDistributionList?.[0]?.deliveryMan,
        });
      }, 500);
      handleGetCstDetail(orderDetail.orders?.cstId!);
      queryCouponList();
    }
  }, [orderDetail]);

  /**
   * 添加商品事件
   */
  const handleAdd = async (itemList: any[]) => {
    const item = itemList[0];
    handleAddFn(item);
  };

  /**
   * 添加商品函数
   * @param item
   */
  const handleAddFn = async (item: any) => {
    // 转换商品数据（提交接口和列表接口的商品字段不一致）
    const { number, price, brandPartNos, oeNos, suggestPrice, unitName, ...rest } = item;
    const goodItem = {
      ...rest,
      brandPartNo: brandPartNos?.join(','),
      oeNo: oeNos?.join(','),
      unitPrice: price,
      saleNum: number,
      unit: unitName,
      originalPrice: suggestPrice,
    };
    if (orderNo) {
      // 添加时
      updateOrderItem({
        orderId: orderDetail?.orderId,
        oprateType: OprateType.Add,
        orderItemList: [goodItem],
      }).then((result) => {
        if (result) {
          message.success(intl.formatMessage({ id: 'sales.order.edit.addSuccess' }));
          queryDetail();
        }
      });
    } else {
      // 创建时
      const cstInfo = await cstFormRef.current?.validateFields?.();
      console.log('cstInfo', cstInfo);
      const extraInfo = await extraFormRef.current?.validateFields?.();
      console.log('extraInfo', extraInfo);

      // 配送地址
      let address = {} as Address;
      if (extraInfo?.addressId) {
        address =
          cstDetail?.addresses?.find((item) => item.id === extraInfo?.addressId) ?? ({} as Address);
      } else {
        address = cstDetail?.addresses?.find((item) => item.isDefault) ?? ({} as Address);
      }

      // 默认联系人
      const contact = cstDetail?.contacts?.find((item) => item.isDefault);

      const params: CreateDraftOrderRequest = {
        draftOrderMain: {
          cstId: cstInfo?.cstId,
          cstName: cstInfo?.cstName,
          storeId: cstInfo?.storeId,
          cstPhone: contact?.phone,
          storeName: cstInfo?.storeName,
          currency: cstInfo?.currency,
          referenceNo: cstInfo?.referenceNo,
          exchangeRate: cstInfo?.rate,
          channel: OrderChannelCode.PC_WORKBENCHES,
          belongingStoreId: cstDetail?.base?.storeId,
          belongingStoreName: cstDetail?.base?.storeName,
          gstExcluded: cstDetail?.settle?.gstExcluded,
          saleCompanyId: cstInfo?.saleCompanyId,
          saleCompanyName: cstInfo?.saleCompanyName,
        },
        draftOrderAddress: address,
        draftOrderPay: {
          payKind: extraInfo.payKind,
          payChannel:
            extraInfo.payKind === PayKind.Cash ? PayChannel.CASH : PayChannel.PAYMENT_DAYS,
        },
        draftOrderDelivery: {
          deliveryMethod: extraInfo?.deliveryMethod,
          warehouseId: cstInfo?.warehouseId,
          warehouseName: cstInfo?.warehouseName,
        },
        draftOrderItemList: [goodItem],
      };
      console.log('params', params);
      // 当现款时默认账户设置
      if (extraInfo.payKind === PayKind.Cash) {
        params.draftOrderPay!.payeeAcount = accountList?.filter(
          (item) => !item.disabled,
        )?.[0]?.value;
        if (params.draftOrderPay!.payeeAcount === ADVANCE_ACCOUNT) {
          params.draftOrderPay!.payChannel = PayChannel.ADVANCE;
          delete params.draftOrderPay!.payeeAcount;
        }
      }
      createDraftOrder(params).then((result) => {
        if (result?.orderNo) {
          message.success(intl.formatMessage({ id: 'sales.order.edit.addSuccess' }));
          history.replace(`/sales/order/edit?orderNo=${result.orderNo}`);
        }
      });
    }
  };

  /**
   * 更新价格/数量事件
   * @param id
   * @param value
   */
  const handleUpdatePriceOrNumber = (data: HandleUpdatePriceOrNumberProps) => {
    const { id, price, number } = data;
    const currentItem = orderDetail?.orderGoodsROList?.find((item) => item.id === id);
    if (currentItem) console.log('UPDATE', price, number);
    updateOrderItem({
      orderId: orderDetail?.orderId,
      oprateType: OprateType.Modify,
      orderItemList: [{ ...currentItem, unitPrice: price, saleNum: number }],
    }).finally(() => {
      queryDetail();
    });
  };

  /**
   * 删除商品事件
   * @param id
   */
  const handleDeleteGoodItem = (id: string) => {
    if (orderDetail?.orderFixedDiscountList?.length) {
      message.warning(intl.formatMessage({ id: 'sales.order.edit.confirmDeleteWithDiscount' }));
      return;
    }
    const currentItem = orderDetail?.orderGoodsROList?.find((item) => item.id === id);
    if (currentItem) {
      updateOrderItem({
        orderId: orderDetail?.orderId,
        oprateType: OprateType.Delete,
        orderItemList: [currentItem],
      }).then((result) => {
        if (result) {
          queryDetail();
        }
      });
    }
  };

  /**
   * 查询客户详情
   */
  const handleGetCstDetail = async (cstId: string) => {
    if (cstId) {
      getCstDetail({ cstId }).then((result) => {
        if (result) {
          setCstDetail(result);
        }
      });
    }
  };

  /**
   * 检查订单已经绑定的优惠券是否可用
   */
  useEffect(() => {
    if (couponList && orderDetail?.orderFixedDiscountList?.[0]?.discountItemId) {
      if (
        !couponList.find(
          (item) =>
            item.userCouponId === Number(orderDetail?.orderFixedDiscountList?.[0]?.discountItemId),
        )
      ) {
        console.log('优惠券不合法');
        updateOrderDiscount({ orderId: orderDetail.orderId, discountType: DiscountType.NONE }).then(
          (result) => {
            if (result) {
              queryDetail();
            }
          },
        );
      }
    }
  }, [couponList, orderDetail]);

  /**
   * 监听客户信息表单值变化
   * @param changedValues
   */
  const handleCstFormDataChange = (changedValues: any) => {
    const keys = Object.keys(changedValues);
    const keyName = keys[0];
    const value = changedValues[keyName];
    console.log('CstFormChangedValues', changedValues);
    switch (keyName) {
      case 'storeId': // 门店变化时清空仓库已选值
        cstFormRef.current?.resetFields(['warehouseId']);
        setStoreId(value);
        break;
      case 'cstId': // 客户变化时获取客户详情信息
        handleGetCstDetail(value);
        break;
      case 'warehouseId':
        setWarehouseId(value);
        if (orderNo) {
          queryDetail();
        }
        break;
      case 'referenceNo':
        if (orderDetail?.orders?.orderId) {
          updateOrderMain({
            orderId: orderDetail?.orders?.orderId,
            referenceNo: value,
          });
        }
        break;
    }
  };

  /**
   * 监听额外信息表单值变化(实时保存)
   */
  const handleExtraFormDataChange = (changedValues: any, allValues: any) => {
    const keys = Object.keys(changedValues);
    const keyName = keys[0];
    const value = changedValues[keyName];
    console.log('ExtraFormChangedValues', changedValues);
    if (orderNo) {
      const orderId = orderDetail?.orderId;
      switch (keyName) {
        case 'saleRemark':
          updateOrderRemark({
            remark: value,
            remarkType: RemarkType.StoreToCustomer,
            orderId: orderDetail?.orderId,
          });
          break;
        case 'innerRemark':
          updateOrderRemark({
            remark: value,
            remarkType: RemarkType.StoreToInner,
            orderId: orderDetail?.orderId,
          });
          break;
        case 'vehicleNos':
          updateOrderRemark({
            remark: value,
            remarkType: RemarkType.VehicleNos,
            orderId: orderDetail?.orderId,
          }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'isPrint':
          updateOrderRemark({
            isPrint: value ? 1 : 0,
            remarkType: RemarkType.StoreToCustomer,
            orderId: orderDetail?.orderId,
          }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'estimatedDeliveryTime': // 更新交货日期
          updateOrderDeliveryInfo({
            orderId: orderDetail?.orderId,
            estimatedDeliveryTime: value,
          }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'discountType': // 更新优惠信息
        case 'discountRate':
        case 'discountMoney':
        case 'couponId':
          const updateOrderDiscountParams = {
            discountType: allValues.discountType,
            orderId,
          } as UpdateOrderDiscountRequest;
          switch (allValues.discountType) {
            case DiscountType.NONE:
              updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                if (result) {
                  queryDetail();
                }
              });
              break;
            case DiscountType.DISCOUNT_ON_ORDER:
              if (changedValues.discountRate) {
                updateOrderDiscountParams.discountRate = (100 - allValues.discountRate) / 10;
                updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                  if (result) {
                    queryDetail();
                  }
                });
              }
              break;
            case DiscountType.DEDUCTION_ON_ORDER:
              if (changedValues.discountMoney) {
                updateOrderDiscountParams.discountMoney = allValues.discountMoney;
                updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                  if (result) {
                    queryDetail();
                  }
                });
              }
              break;
            case DiscountType.COUPON:
              if (changedValues.couponId) {
                updateOrderDiscountParams.couponId = allValues.couponId;
                updateOrderDiscountParams.discountMoney = couponList.find(
                  (item) => item.userCouponId === allValues.couponId,
                )?.couponAmount;
                updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                  if (result) {
                    queryDetail();
                  }
                });
              }
              break;
          }
          break;
        case 'deliveryAmount': // 更新运费
          updateExtendExpense({ orderId, deliveryAmount: value }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'deliveryMethod': // 更新配送信息
          updateOrderDeliveryInfo({ orderId, deliveryMethod: value }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'addressId':
          const address = cstDetail?.addresses?.find((item) => item.id === allValues.addressId);
          if (address) {
            updateOrderAddress({ orderId, ...address }).then((result) => {
              if (result) {
                queryDetail();
              }
            });
          }
          break;
        case 'deliveryMan':
          updateOrderDeliveryInfo({ orderId, deliveryMan: value }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'operatorType':
          const updateOrderTagParams: UpdateOrderTagRequest = {
            orderId,
            operatorType: allValues.operatorType ? 1 : 2,
            tagId: 2,
          };
          updateOrderTag(updateOrderTagParams).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'logisticsCompanyName':
        case 'logisticsNo':
          updateOrderDeliveryInfo({
            orderId,
            logisticsCompanyName: allValues.logisticsCompanyName,
            logisticsNo: allValues.logisticsNo,
          }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'adjustmentType':
        case 'adjustment':
        case 'adjustmentReason':
          const adjustmentParams: UpdateAdjustmentRequest = {
            orderId,
            adjustmentType: allValues.adjustmentType,
          };
          if (allValues.adjustmentType === AdjustAmountType.Custom) {
            if (allValues.adjustment) {
              adjustmentParams.adjustment = -allValues.adjustment;
              adjustmentParams.adjustmentReason = allValues.adjustmentReason;
              updateAdjustment(adjustmentParams).then((result) => {
                if (result) {
                  queryDetail();
                }
              });
            }
          } else {
            updateAdjustment(adjustmentParams).then((result) => {
              if (result) {
                queryDetail();
              }
            });
          }
          break;
        case 'payKind':
        case 'payDetailList':
          const payKindParams = {
            orderId,
            payKind: allValues.payKind,
            payDetailList: [],
          } as ConfirmPayRequest;
          let PayInfoFinished = true;
          switch (allValues.payKind) {
            case PayKind.Credit:
              payKindParams.payDetailList?.push({
                payChannel: PayChannel.PAYMENT_DAYS,
                payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
              });
              break;
            case PayKind.Cash:
              allValues.payDetailList?.forEach((item: any) => {
                if (
                  typeof item?.payeeAcount !== 'undefined' &&
                  typeof item?.payAmount !== 'undefined'
                ) {
                  if (item.payeeAcount === ADVANCE_ACCOUNT) {
                    payKindParams.payDetailList?.push({
                      payAmount: item.payAmount,
                      payChannel: PayChannel.ADVANCE,
                    });
                  } else {
                    payKindParams.payDetailList?.push({
                      ...item,
                      payChannel: PayChannel.CASH,
                    });
                  }
                }
              });
              if (payKindParams.payDetailList?.length === 0) {
                const defaultItem = {
                  payChannel: PayChannel.CASH,
                  payeeAcount: accountList?.filter((item) => !item.disabled)?.[0]?.value,
                  payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
                };
                if (defaultItem.payeeAcount === ADVANCE_ACCOUNT) {
                  defaultItem.payChannel = PayChannel.ADVANCE;
                  delete defaultItem.payeeAcount;
                }
                payKindParams.payDetailList.push(defaultItem);
              }
              console.log(
                'PayInfoFinished',
                allValues.payDetailList?.length,
                payKindParams?.payDetailList?.length,
              );
              PayInfoFinished =
                (allValues.payDetailList?.length ?? 0) <=
                (payKindParams?.payDetailList?.length ?? 0);
              break;
          }
          if (PayInfoFinished) {
            updateOrderPayKind(payKindParams).then((result) => {
              if (result) {
                queryDetail();
              }
            });
          }
          break;
      }
    }
  };

  return (
    <PageContainer>
      <ProCard>
        <ProForm
          formRef={cstFormRef}
          submitter={false}
          layout="vertical"
          grid={true}
          onValuesChange={cstFormChange.run}
          className="-mb-4"
        >
          <CstForm
            // @ts-ignore
            cstFormRef={cstFormRef}
            orderId={orderDetail?.orderId}
            orderNo={orderNo as string}
            warehouseOptions={warehouseOptions}
            setWarehouseId={setWarehouseId}
            setStoreId={setStoreId}
            isMultiCurrency={cstDetail?.settle?.isMultiCurrency === 1}
          />
        </ProForm>
        <CstDetail cstDetail={cstDetail} />
      </ProCard>
      <ProCard className="mt-4" bodyStyle={{ paddingTop: 8 }}>
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                controlWidth: 80,
              },
            },
          }}
        >
          <GoodsSearch
            bizType={GoodsSearchBizType.Sales}
            onAdd={handleAdd}
            addedItemSns={orderDetail?.orderGoodsROList?.map((item) => item.itemSn!) ?? []}
            warehouseId={warehouseId}
            cstId={cstDetail?.base?.id}
            storeId={storeId}
            saleOrderNo={orderNo as string}
          />
        </ConfigProvider>
      </ProCard>
      <ProCard className="mt-4" bodyStyle={{ paddingTop: 0 }}>
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                controlWidth: 80,
              },
            },
          }}
        >
          <SalesList
            orderDetail={orderDetail}
            handleUpdatePriceOrNumber={handleUpdatePriceOrNumber}
            handleDeleteGoodItem={handleDeleteGoodItem}
            cstId={cstDetail?.base?.id}
            storeId={storeId}
            warehouseId={warehouseId}
            orderNo={orderNo}
          />
        </ConfigProvider>
        <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 8 } } }}>
          <ProForm
            submitter={false}
            formRef={extraFormRef}
            disabled={!Boolean(orderNo)}
            onValuesChange={extraFormChange.run}
            className="mt-[20px]"
          >
            <Row gutter={10}>
              <Col span={4}>
                <DiscountForm couponList={couponList} />
              </Col>
              <Col span={3}>
                <ProFormDigit
                  label={
                    <span className="font-semibold">
                      {intl.formatMessage({ id: 'sales.order.edit.deliveryAmount' })}
                    </span>
                  }
                  name="deliveryAmount"
                  min={0}
                  placeholder={intl.formatMessage({ id: 'sales.order.edit.setDeliveryAmount' })}
                  fieldProps={{
                    precision: 2,
                    controls: false,
                  }}
                />
              </Col>
              <Col span={4}>
                <AdjustAmountForm />
              </Col>
              <Col span={6}>
                <PaymentForm cstDetail={cstDetail} orderDetail={orderDetail} />
              </Col>
              <Col span={4}>
                <DeliveryForm cstDetail={cstDetail} />
              </Col>
              <Col span={3}>
                <ProFormDatePicker
                  // @ts-ignore
                  width="100%"
                  label={
                    <span className="font-semibold">
                      {intl.formatMessage({ id: 'sales.order.edit.estimatedDeliveryTime' })}
                    </span>
                  }
                  name="estimatedDeliveryTime"
                  placeholder={intl.formatMessage({
                    id: 'sales.order.edit.estimatedDeliveryTimePlaceholder',
                  })}
                />
                <div className="flex items-center gap-2">
                  <span className="mb-2">紧急订单</span>
                  <ProFormSwitch name="operatorType" />
                </div>
              </Col>
            </Row>
            <Row gutter={10}>
              <Col span={8}>
                <ProFormItem label={<span className="font-semibold">车牌</span>} name="vehicleNos">
                  <ProFormChipTagInput />
                </ProFormItem>
              </Col>
              <Col span={8}>
                <ProFormTextArea
                  label={
                    <div className="flex justify-between items-center whitespace-nowrap">
                      <span className="font-semibold">
                        {intl.formatMessage({ id: 'sales.order.edit.saleRemark' })}
                      </span>
                      <label className="ml-5 flex gap-2 items-center h-[20px]">
                        <div className="relative top-1">
                          <ProFormCheckbox name="isPrint" />
                        </div>
                        {intl.formatMessage({ id: 'sales.order.edit.printRemark' })}
                      </label>
                    </div>
                  }
                  name="saleRemark"
                  formItemProps={{}}
                  fieldProps={{
                    count: { max: 100, show: true },
                    maxLength: 100,
                  }}
                />
              </Col>
              <Col span={8}>
                <ProFormTextArea
                  label={
                    <span className="font-semibold">
                      {intl.formatMessage({ id: 'sales.order.edit.innerRemark' })}
                    </span>
                  }
                  name="innerRemark"
                  fieldProps={{
                    count: { max: 100, show: true },
                    maxLength: 100,
                  }}
                />
              </Col>
            </Row>
          </ProForm>
        </ConfigProvider>
      </ProCard>
      <ProCard className="mt-[1px]">
        <SumBar orderDetail={orderDetail} />
      </ProCard>
    </PageContainer>
  );
};

export default withKeepAlive(SalesOrderEdit);
