export default {
  order: {
    list: {
      // 表格列标题
      orderNo: '销售单号',
      customerName: '客户名称',
      orderTime: '下单时间',
      orderAmount: '订单金额',
      grossProfit: '销售毛利',
      orderStatus: '订单状态',
      settlementAccount: '结算账户',
      settlementStatus: '结算状态',
      paymentStatus: '收款状态',
      payTime: '结算时间',
      outboundTime: '出库时间',
      finishTime: '完成时间',
      productInfo: '商品信息',
      salesStore: '销售门店',
      deliveryWarehouse: '发货仓库',
      creator: '制单人',

      storePC: '门店工作台',
      storeH5: '移动工作台',
      ecommerce: '商城',

      // 搜索占位符
      productSearchPlaceholder: '商品编码/商品名称/OE/供应商编码',

      // 按钮文本
      addSales: '新增销售',
      import: '导入',

      // 操作按钮
      edit: '编辑',
      withdraw: '撤回',
      void: '作废',
      print: '打印',

      // 发送邮件
      sendSuccess: '发送成功',
      sendEmail: '发送邮件',
      receiveMan: '收件人',
      carbonCopy: '抄送',
      bcc: '密送',
      sendPlaceholder: '回车以输入多个邮箱',

      // 确认消息
      confirmWithdraw: '确认撤回吗',
      confirmVoid: '是否确定作废订单',

      // 成功消息
      withdrawSuccess: '撤回成功',
      voidSuccess: '作废成功',

      // Tab标签
      all: '全部',

      // 统计文本
      totalOrderCount: '总销售单数',
      totalSalesAmount: '总销售金额',
      totalGrossProfit: '总销售毛利',

      // 账户
      creditAccount: '挂账',
      advanceAccount: '预收',

      // 订单状态
      status: {
        draft: '草稿',
        waitToOutbound: '待出库',
        outboundFinish: '已出库',
        tradeSuccess: '已完成',
        tradeClose: '已作废',
      },

      // 结算状态
      payStatus: {
        waitToPay: '未结算',
        partPay: '部分结算',
        allPay: '已结算',
        paying: '结算中',
      },

      // 收款状态
      receiptPaymentStatus: {
        unPay: '未收款',
        partPay: '部分收款',
        allPay: '已收款',
      },

      // 开票状态
      receiptStatus: {
        notInvoiced: '未开票',
        invoiced: '已开票',
      },
    },

    detail: {
      // 基础信息
      customerDetail: '客户详情',
      viewCustomerDetail: '查看客户详情',
      customerName: '客户名称',
      orderStatus: '订单状态',
      orderAmount: '订单金额',
      settlementMethod: '结算方式',
      salesStore: '销售门店',
      belongingStoreName: '业绩归属门店',
      deliveryWarehouse: '发货仓库',
      orderTime: '下单时间',
      estimatedDeliveryTime: '期望送达时间',
      creator: '制单人',

      orderRemark: '订单备注',
      urgency: '紧急',

      // 操作按钮
      edit: '编辑',
      audit: '审核',
      withdraw: '撤回',
      void: '作废',
      print: '打印',
      receive: '客户收款',
      confirmSettlement: '确认结算',
      oneClickOutbound: '一键出库',

      // 确认消息
      confirmWithdraw: '是否确认撤回',
      confirmVoid: '是否确定作废订单',
      confirmOneClickOutbound: '是否确认一键出库',

      // 成功消息
      withdrawSuccess: '撤回成功',
      voidSuccess: '作废成功',
      outboundSuccess: '出库成功',
      settlementSuccess: '结算成功',

      // 商品明细
      goodsDetail: '商品明细',
      productCode: '商品编码',
      productName: '商品名称',
      oeNumber: 'OE',
      brandPartNumber: '供应商编码',
      brand: '品牌',
      category: '分类',
      unit: '单位',
      salePrice: '销售价',
      discountPrice: '实付单价',
      saleQuantity: '销售数量',
      subtotal: '小计',

      // 结算记录
      settlementRecord: '结算记录',
      settlementTime: '结算时间',
      settlementAccount: '结算账户',
      settlementAmount: '结算金额',

      // 配送信息
      deliveryInfo: '配送信息',
      deliveryMethod: '配送方式',
      logisticsCompany: '物流公司',
      logisticsNumber: '物流单号',
      deliveryAddress: '配送地址',

      // 操作记录
      operationRecord: '操作记录',
      operationTime: '操作时间',
      orderNode: '操作内容',
      operator: '操作人',

      // 采购需求
      purchaseRequest: '采购需求',
      purchaseSupplierName: '供应商名称',
      purchaseNum: '数量',

      auditGoods: '待审核商品',
      auditMan: '审核人',
      auditDate: '审核时间',
      auditReason: '审核不通过原因',

      printLoading: '正在打印，请稍后...',
    },

    edit: {
      // 表单标签
      customerName: '客户名称',
      salesStore: '销售门店',
      deliveryWarehouse: '发货仓库',
      discountType: '优惠类型',
      deliveryMethod: '配送方式',
      estimatedDeliveryTime: '期望送达时间',
      remark: '备注',
      deliveryAmount: '运费',
      saleRemark: '销售备注',
      returnRemark: '退货备注',
      innerRemark: '内部备注',
      printRemark: '打印销售备注',

      // 占位符
      selectCustomer: '请选择客户',
      selectSalesStore: '请选销售门店',
      selectDeliveryWarehouse: '请选发货仓库',
      selectDeliveryAddress: '请选择配送地址',
      selectDeliveryMan: '请选择配送员',
      inputLogisticsCompany: '请输入物流公司名称',
      inputLogisticsNumber: '请输入快递单号',
      inputDiscount: '请输入折扣',
      inputAmount: '请输入金额',
      setDeliveryAmount: '请输入运费',
      estimatedDeliveryTimePlaceholder: '预计送达时间',
      inputWithMaxLength: '请输入，最多{maxLength}个字符',

      //
      adjustAmount: '调整金额',
      adjustAmountNone: '无',
      adjustAmountRound: '整单抹零',
      adjustAmountCustom: '其他调整',
      adjustAmountCustomPrice: '调整金额',
      adjustAmountCustomReason: '调整原因',

      // 销售明细
      salesDetail: '销售明细',
      salesOrderNo: '销售单号',
      salesStatus: '销售状态',

      // 表格列标题
      index: '序号',
      productCode: '商品编码',
      productName: '商品名称',
      oe: 'OE',
      brandPartNo: '供应商编码',
      brand: '品牌',
      category: '分类',
      origin: '产地',
      specification: '规格',
      unit: '单位',
      vehicleRemark: '车型备注',
      productRemark: '商品备注',
      localStock: '本地库存',
      location: '库位',
      suggestedPrice: '建议售价',
      lastSalePrice: '上次售价',
      lowestPrice: '最低售价',
      costPrice: '成本价',
      grossMargin: '销售毛利',
      salePrice: '销售价',
      saleQuantity: '销售数量',
      operation: '操作',

      // 按钮文本
      delete: '删除',
      add: '添加',
      submit: '提交',
      confirmSettlement: '确认结算',
      oneClickOutbound: '一键出库',
      submitAndPrint: '提交后打印',

      // 确认消息
      confirmAddOverStock: '销售数量大于可用库存，是否确认添加',
      confirmDeleteWithDiscount: '请先清空优惠类型再删除商品',

      // 成功消息
      addSuccess: '添加成功',

      // 优惠类型
      discountTypes: {
        none: '无折扣',
        orderDiscount: '整单折',
        orderDeduction: '整单减',
        coupon: '优惠券',
      },

      // 配送方式
      deliveryMethods: {
        selfPickup: '客户自提',
        merchantDelivery: '商家配送',
        expressLogistics: '快递物流',
      },

      // 单位
      discountUnit: '折',
      amountUnit: '元',

      // 统计信息
      totalQuantity: '商品总数',
      totalAmount: '商品总金额',
      discountAmount: '优惠金额',
      deliveryFee: '运费',
      actualAmount: '销售总金额',

      // 客户详情
      customerDetail: '客户详情',
      creditCustomer: '挂账客户',
      contact: '联系人',
      contactMethod: '联系方式',
      creditTerm: '信用账期',
      creditLimit: '信用额度',
      used: '已用',
      available: '可用',
      days: '天',
    },
  },

  returns: {
    list: {
      // 表格列标题
      returnOrderNo: '退货单号',
      customerName: '客户名称',
      customer: '客户',
      returnTime: '退货时间',
      orderStatus: '单据状态',
      returnStore: '退货门店',
      receiveWarehouse: '收货仓库',
      refundAmount: '退货金额',
      settlementMethod: '结算方式',
      settlementStatus: '结算状态',
      settlementAccount: '结算账户',
      productInfo: '商品信息',
      creator: '制单人',
      operation: '操作',

      // 搜索占位符
      productSearchPlaceholder: '商品名称/编码/OE/供应商编码',

      // 按钮文本
      addSalesReturn: '新增销售退货',
      export: '导出',
      edit: '编辑',
      withdraw: '撤回',
      void: '作废',

      // 确认消息
      confirmWithdraw: '是否确定撤回订单？',
      confirmVoid: '是否确定作废订单？',

      // 统计文本
      totalReturnCount: '总退货单数',
      totalReturnAmount: '总退货金额',

      // 导出任务描述
      exportTaskDesc: '零售商售后单导出',
    },

    detail: {
      // 基础信息
      customerDetail: '客户详情',
      viewCustomerDetail: '查看客户详情',
      customerName: '客户名称',
      orderStatus: '单据状态',
      refundAmount: '退货金额',
      settlementMethod: '结算方式',
      returnStore: '退货门店',
      receiveWarehouse: '收货仓库',
      orderTime: '下单时间',
      creator: '制单人',
      remark: '备注',
      returnImage: '退货图片',

      // 操作按钮
      edit: '编辑',
      withdraw: '撤回',
      void: '作废',
      print: '打印',
      oneClickInbound: '一键入库',
      confirmSettlement: '确认结算',

      // 确认消息
      confirmWithdraw: '是否确定撤回订单？',
      confirmVoid: '是否确定作废订单？',
      confirmOneClickInbound: '是否确定一键入库？',

      // 表格标题
      goodsDetail: '商品明细',
      settlementRecord: '结算记录',
      operationRecord: '操作记录',
      settlementTime: '结算时间',

      // 统计信息
      totalQuantity: '商品总数',
      totalReturnAmount: '退货总金额',

      // 编辑页面名称
      editPageName: '销售退货编辑',

      // 退货原因
      returnReason: '退货原因',
    },

    // 退货操作页面
    operation: {
      // 表单标签
      customer: '客户',
      selectCustomer: '请选择客户',
      returnStore: '退货门店',
      selectReturnStore: '请选择退货门店',
      returnWarehouse: '收货仓库',
      selectReturnWarehouse: '请选择收货仓库',
      settlementMethod: '结算方式',
      remark: '备注',
      maxCharacters: '最多支持200个字符！',

      // 退货方式
      salesOrderReturn: '销售单退货',
      salesOrderReturnDesc: '选择销售单里的商品退货',
      goodsReturn: '商品退货',
      goodsReturnDesc: '选择商品退货',

      // 表格标题
      returnDetails: '退货明细',
      returnOrderNo: '退货单号',
      returnStatus: '退货状态',

      // 操作按钮
      delete: '删除',
      submit: '提交',
      return: '退货',

      // 消息提示
      addSuccess: '添加成功',
      operationSuccess: '操作成功',
      submitSuccess: '订单提交成功！',
      maxGoodsWarning: '加购商品数量不能超过200',
      inputReturnAmount: '请输入退货金额！',
      inputReturnQuantity: '请输入退货数量！',
      fillReturnAmount: '请填写退货金额！',
      fillReturnQuantity: '请填退货数量！',
      selectDifferentAccount: '请选择不同的账户！',
      selectAccount: '请选择账户',
      inputAmount: '请输入金额',

      // 结算相关
      confirmSettlement: '确认结算',
      directInbound: '一键入库',
      printAfterSubmit: '提交后打印',
      usedAvailable: '已用{used}/可用{available}',

      // 汇总信息
      totalQuantity: '商品总数',
      totalGoodsAmount: '商品总金额',
      totalRefundAmount: '退货总金额',

      // 搜索相关
      productInfo: '商品信息',
      productSearchPlaceholder: '商品名称/编码/OE/供应商编码',

      // 表格列标题
      salesOrderNo: '销售单号',
      salesTime: '销售时间',
      productCode: '商品编码',
      productName: '商品名称',
      oe: 'OE',
      brandPartNo: '供应商编码',
      brand: '品牌',
      salesStore: '销售门店',
      deliveryWarehouse: '发货仓库',
      paymentMethod: '结算方式',
      actualUnitPrice: '实付单价',
      salesQuantity: '销售数量',
      refundableQuantity: '可退数量',
      refundAmount: '退货单价',
      returnQuantity: '退货数量',

      causeType: {
        PRODUCT_NOT_MATCH: '产品不适配',
        PRODUCT_QUALITY: '产品质量问题',
        BUYER_NOT_WANT: '车主不要了',
        INVENTORY_RETURN: '库存退货',
        PACKAGE_BROKEN: '包装破损',
        OTHER: '其他',
      },

      returnType: {
        label: '退货性质',
        good: '好件',
        broken: '坏件',
      },
    },

    settlement: {
      // 结算方式模态框
      title: '结算方式',
      settlementAmount: '结算金额',
      settlementMethod: '结算方式',
      remainingCredit: '剩余额度',
      used: '已用',
      available: '可用',
      selectAccount: '请选择账户',
      inputAmount: '请输入金额',
      confirm: '确认',
      duplicateAccountError: '请选择不同的账户！',
    },
  },
};
