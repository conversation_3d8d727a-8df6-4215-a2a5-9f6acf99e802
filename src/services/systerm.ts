import { getSeesionCookie } from '@/access';
import { queryUserInfoPost } from '@/pages/common/login/services';
import type { MenuTreeEntity } from '@/pages/system/role/list/types/menu.tree.entity';
import { fetchMenuDataPost, menuListQueryPost } from '@/pages/system/role/services';
import type { CreateTaskParam } from '@/types/CreateTaskType';
import type { QuerySysPropertyListRequest } from '@/types/query.sys.property.list.request';
import type { QuerySysPropertyListResponse } from '@/types/query.sys.property.list.response';
import type { QueryTaskResultResponse } from '@/types/query.task.result.response';
import type { SetSysPropertyRequest } from '@/types/set.sys.property.request';
import { transformRemoteMenuToMenu, transformRouteToMenu } from '@/utils/formatMenuData';
import { request } from '@/utils/request';
import type { MenuDataItem } from '@umijs/route-utils';
import routes from '../../config/routes';

/**
 * 查询初始化系统信息
 */
export const getInitSystemInfo = async () => {
  // 查询用户信息
  const sSessionId = getSeesionCookie();
  let currentUserResponse;
  let currentUserInfo;
  if (sSessionId) {
    currentUserResponse = await queryUserInfoPost({});
    currentUserInfo = await getCurrentUserInfo({});
  }
  let systemMenu: MenuDataItem[] = [];
  let buttonItem: string[] = [];
  let remoteMenuData: MenuTreeEntity[] = [];
  if (currentUserResponse?.data) {
    // 查询菜单信息
    if (USE_REMOTE_MENU) {
      remoteMenuData = await fetchMenuDataPost({});
      systemMenu = transformRemoteMenuToMenu(routes, remoteMenuData);
      //查询按钮信息
      buttonItem = await menuListQueryPost({ type: '2' });
    } else {
      systemMenu = transformRouteToMenu(routes);
    }
  }
  return {
    currentUser: currentUserResponse?.data ?? {},
    currentUserInfo: currentUserInfo ?? {},
    systemMenu,
    remoteMenuData,
    buttonItem,
  };
};

/**
 * 上传文件
 */
export const uploadFile = (formData: FormData) => {
  return request<string[]>('/public/upload/object/batch', { data: formData });
};

/**
 * 根据文件名下载文件
 */
export const downloadFile = async (fileName: string | undefined) => {
  const fileUrl = await request<string>(
    '/public/upload/generateTempPublicUrl?fileName=' + fileName,
    { method: 'GET' },
  );
  window.open(fileUrl);
};

/**
 * 创建导入任务
 */
export const createImportTask = (params: CreateTaskParam) => {
  return request<{ taskId: string }>('/ipmsgie/SaasImportExportTaskFacade/createTask', {
    data: params,
  });
};

/**
 * 导入导出任务状态查询
 */
export const queryTaskResult = (taskId: string, systemId: string) => {
  return request<QueryTaskResultResponse>('/ipmsgie/SaasImportExportTaskFacade/queryTaskResult', {
    data: { taskId, systemId },
  });
};

/**
 * 创建导出任务
 */
export const createExportTask = (params: CreateTaskParam) => {
  return request<{ taskId: string }>('/ipmsgie/SaasImportExportTaskFacade/createTask', {
    data: params,
  });
};

/**
 * 获取系统配置
 */
export const querySysPropertyList = (params: QuerySysPropertyListRequest) => {
  return request<QuerySysPropertyListResponse[]>(
    '/ipmsconsole/console/SysPropertyFacade/querySysPropertyList',
    {
      data: params,
    },
  );
};

/**
 * 设置系统配置
 */
export const setSysProperty = (params: SetSysPropertyRequest) => {
  return request<boolean>('/ipmsconsole/console/SysPropertyFacade/setSysProperty', {
    data: params,
  });
};

/**
 * 查询账户基础信息
 * @param params
 * @returns
 */
export const getCurrentUserInfo = (params: {}) => {
  return request<any>('/ipmspassport/AccountFacade/info', {
    data: params,
  });
};


// 通用打印接口
export const getPrintUrl = (params: {
  // 业务单号，用于打印文件名
  orderNo?: string;
  // 跳转地址
  redirectUrl: string;
  // false表示纵向，true表示横向
  landscape?: boolean;
}): Promise<{
  url: string;
}> => {
  return request('/ipmsupload/UploadFacade/getPrintUrl', {
    data: params,
  })
};