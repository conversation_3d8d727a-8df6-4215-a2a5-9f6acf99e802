import { useEffect, useState } from 'react';
import { goodsDetail } from '@/pages/goods/list/services';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { Card, Tag, Image, Empty } from 'antd';
import LeftTitle from '@/components/LeftTitle';
import { ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from 'umi';
import { ChannelCodeEnum } from '@/pages/goods/list/types/channel.code';
import BraftEditor, { EditorState } from 'braft-editor';
import { GoodsStatusValueEnum } from '@/pages/goods/list/types/GoodsStatusValueEnum';

export interface GoodsInfoProps {
  itemId?: string;
}

export default function GoodsInfo(props: GoodsInfoProps) {
  const { itemId } = props;
  const [detail, setDetail] = useState<GoodsEntity>();
  const intl = useIntl();
  const [content, setContent] = useState<EditorState>('');

  useEffect(() => {
    if (itemId) {
      goodsDetail({ itemId }).then((res) => {
        // @ts-ignore
        setDetail(res);
        // @ts-ignore
        if (res.ecommerceDetail && res.ecommerceDetail !== '<p></p>') {
          // @ts-ignore
          setContent(BraftEditor.createEditorState(res.ecommerceDetail));
        }
      });
    }
    return () => {
      setDetail(undefined);
      setContent('');
    };
  }, [itemId]);

  return (
    <div className="!bg-gray-100 -m-[16px] p-[16px] flex flex-col gap-4">
      <Card className="border-none rounded-xl">
        <div className="flex items-center gap-3">
          <span className="text-[20px] font-medium">{detail?.itemName}</span>
          <Tag
            color={
              // @ts-ignore
              GoodsStatusValueEnum[detail?.itemStatus?.toString()]?.status === 'Success'
                ? 'green'
                : 'red'
            }
          >
            {/**@ts-ignore**/}
            {GoodsStatusValueEnum[detail?.itemStatus?.toString()]?.text}
          </Tag>
        </div>
        <ProDescriptions
          className="mt-5"
          column={4}
          dataSource={detail}
          columns={[
            {
              title: intl.formatMessage({
                id: 'goods.createForm.itemSnLabel',
              }),
              dataIndex: 'itemSn',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.channelLabel' }),
              dataIndex: 'channelCodeList',
              render: (text, record) => {
                return record.channelCodeList?.map((item, index) => {
                  return (
                    <>
                      {index > 0 ? ', ' : ''}
                      {/** @ts-ignore**/}
                      {ChannelCodeEnum[Number(item)]?.text.valueOf()}
                    </>
                  );
                });
              },
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.oeLabel' }),
              dataIndex: 'oeNos',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.brandPartNoLabel' }),
              dataIndex: 'brandPartNos',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.brandLabel' }),
              dataIndex: 'brandName',
            },
            {
              title: intl.formatMessage({
                id: 'goods.createForm.categoryLabel',
              }),
              dataIndex: 'categoryName',
            },
            {
              title: intl.formatMessage({
                id: 'goods.createForm.realBrandLabel',
              }),
              dataIndex: 'realBrandName',
            },
            {
              title: intl.formatMessage({
                id: 'goods.createForm.realBrandPartNoLabel',
              }),
              dataIndex: 'realBrandPartNo',
            },
            {
              title: intl.formatMessage({
                id: 'goods.createForm.ownCodeLabel',
              }),
              dataIndex: 'ownCode',
            },
            {
              title: intl.formatMessage({
                id: 'goods.createForm.orderCodeLabel',
              }),
              dataIndex: 'orderCode',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.barCodeLabel' }),
              dataIndex: 'barCodeList',
              render: (_text, record) => {
                return record.barCodeList?.join(', ');
              },
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.unitLabel' }),
              dataIndex: 'unitName',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.specLabel' }),
              dataIndex: 'spec',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.originRegionLabel' }),
              dataIndex: 'originRegionName',
            },
            {
              title: intl.formatMessage({
                id: 'goods.createForm.skuSizeLabel',
              }),
              dataIndex: 'dimensionUnit',
              render: (text, record) => {
                return (
                  <div>
                    {record.skuLength}*{record.skuWidth}*{record.skuHeight}
                    {record.dimensionUnit}
                  </div>
                );
              },
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.skuWeightLabel' }),
              dataIndex: 'skuWeight',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.memCodeLabel' }),
              dataIndex: 'memCode',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.adaptModelLabel' }),
              dataIndex: 'adaptModel',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.remarkLabel' }),
              dataIndex: 'remark',
            },
            {
              dataIndex: '',
              render: () => '',
            },
            {
              title: intl.formatMessage({ id: 'goods.createForm.imagesLabel' }),
              dataIndex: 'images',
              span: 6,
              render: (_text, record) => {
                return (
                  <Image.PreviewGroup>
                    <div className="flex gap-3">
                      {record.images?.map((item, index) => (
                        <div className="relative border border-solid border-[#260D0D0D]">
                          <Image width={100} src={item} />
                          {index === 0 && (
                            <div className="absolute left-0 top-0 px-1 border border-solid rounded-[2px] text-[12px] text-[#F49C1F] border-[#FBD7A5] bg-[#FDF0D8]">
                              {intl.formatMessage({ id: 'goods.createForm.mainImage' })}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </Image.PreviewGroup>
                );
              },
            },
          ]}
        />
      </Card>
      <Card className="border-none rounded-xl">
        <LeftTitle title="采购信息" />
        <div>
          <ProDescriptions
            className="mt-5"
            column={4}
            dataSource={detail}
            columns={[
              {
                title: intl.formatMessage({
                  id: 'goods.createForm.minPurchaseStock',
                }),
                dataIndex: 'minPurchaseStock',
              },
              {
                title: intl.formatMessage({
                  id: 'goods.createForm.maxPurchaseStock',
                }),
                dataIndex: 'maxPurchaseStock',
              },
            ]}
          />
          <div className="flex flex-col gap-3 mt-2">
            {detail?.supplierList?.map((item, index) => (
              <div className="border border-solid border-[#260D0D0D] rounded px-4 pb-2">
                <ProDescriptions
                  className="mt-5"
                  column={4}
                  dataSource={item}
                  columns={[
                    {
                      title: intl.formatMessage({
                        id: 'goods.createForm.supplierLabel',
                      }),
                      dataIndex: 'supplierName',
                      render: (text, record) => {
                        return (
                          <span className="flex gap-2 items-center">
                            {text}
                            {record.isDefault === 1 && (
                              <Tag color={'blue'}>
                                {intl.formatMessage({
                                  id: 'goods.createForm.supplier.default',
                                })}
                              </Tag>
                            )}
                          </span>
                        );
                      },
                    },
                    {
                      title: intl.formatMessage({
                        id: 'goods.createForm.purchasePriceLabel',
                      }),
                      dataIndex: 'purchasePrice',
                    },
                    {
                      title: intl.formatMessage({
                        id: 'goods.createForm.moq',
                      }),
                      dataIndex: 'moq',
                    },
                    {
                      title: intl.formatMessage({
                        id: 'goods.createForm.mpq',
                      }),
                      dataIndex: 'mpq',
                    },
                  ]}
                />
              </div>
            ))}
          </div>
        </div>
      </Card>
      <Card className="border-none rounded-xl">
        <LeftTitle title="价格信息" />
        <div className="grid grid-cols-4 my-3 leading-8">
          <span>
            {intl.formatMessage({
              id: 'goods.createForm.suggestPriceLabel',
            })}
            : {detail?.suggestPrice}
          </span>
          <span>
            {intl.formatMessage({
              id: 'goods.createForm.lowPriceLabel',
            })}
            : {detail?.lowPrice}
          </span>
          {detail?.priceDetails?.map((item) => (
            <span>
              {item.levelName}: {item.levelPrice}
            </span>
          ))}
        </div>
      </Card>
      <Card className="border-none rounded-xl">
        <LeftTitle title="商城详情" />
        {content ? (
          <div className="richText" dangerouslySetInnerHTML={{ __html: content.toHTML() }} />
        ) : (
          <Empty />
        )}
      </Card>
    </div>
  );
}
