import { getCstList } from "@/pages/customer/list/services";
import { getOtherRelatedCompanyList } from "@/pages/finance/otherRelated/service";
import { querySupplierList } from "@/pages/purchase/supplier/services";
import { FormInstance, ProFormSelect, ProFormSelectProps, ProFormText } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";
import { useEffect, useState } from "react";


// 与收支对象类型类型一致
export enum ObjectType {
  // 供应商
  Suppler = 3,
  // 客户
  Customer = 1,
  // 其他往来单位
  OtherCompany = 2,
}

interface ProFormObjectProps {
  label?: string;
  objects: ObjectType[];
  form: FormInstance;
  fieldsName: {
    fieldType: string;
    fieldName: string;
    fieldId: string;
  }
}
const ProFormObject = (props: ProFormObjectProps & ProFormSelectProps) => {
  const { objects, fieldsName, form, required, ...rest } = props;
  const { fieldType = 'objectType', fieldName = 'objectName', fieldId = 'objectId' } = fieldsName || {};
  const inlt = useIntl();
  const t = (id) => inlt.formatMessage({ id });

  const [objectType, setObjectType] = useState<ObjectType>(objects[0]);

  useEffect(() => {
    props.form?.setFieldsValue({
      [fieldId]: undefined,
      [fieldName]: undefined,
    });
  }, [objectType, props.form]);

  const selectProps: ProFormSelectProps = {
    width: 200,
    name: fieldId,
    label: props.label ? " " : "",
    colon: false,
    showSearch: true,
    onChange: (value, option) => {
      props.form.setFieldsValue({
        [fieldName]: option?.title,
      });
    },
    disabled: props.disabled,
    debounceTime: 300,
  };

  return (
    <div className="flex">
      <div className="flex-1">
        <ProFormSelect
          width={'100%'}
          label={props.label}
          required={props.required}
          name={fieldType}
          options={[
            { label: t('common.object.supplier'), value: ObjectType.Suppler },
            { label: t('common.object.customer'), value: ObjectType.Customer },
            { label: t('common.object.otherCompany'), value: ObjectType.OtherCompany },
          ].filter((item) => props.objects.includes(item.value))}
          formItemProps={{
            initialValue: objects?.[0],
          }}
          {...rest}
          onChange={(value) => {
            setObjectType(value);
            props.onChange?.(undefined);
          }}
          allowClear={false}
        />
      </div>
      <div className="flex-1">
        {objectType === ObjectType.Customer && <ProFormSelect
          {...selectProps}
          hidden={props.disabled}
          request={({ keyWords }) => {
            return getCstList({ cstStatus: 0, keyword: keyWords }).then((data) => {
              return data?.map(({ cstId, cstName }) => ({
                value: cstId,
                label: cstName,
              }));
            });
          }} />}
        {
          objectType === ObjectType.OtherCompany && <ProFormSelect
            {...selectProps}
            hidden={props.disabled}
            request={
              ({ keyWords }) => getOtherRelatedCompanyList({ keyword: keyWords }).then((data) => {
                return data?.map(({ companyId, companyName }) => ({
                  value: companyId,
                  label: companyName,
                }));
              })
            } />
        }
        {
          objectType === ObjectType.Suppler && <ProFormSelect
            {...selectProps}
            hidden={props.disabled}
            request={
              ({ keyWords }) => querySupplierList({ keyword: keyWords }).then((data) => {
                return data?.map(({ supplierId, supplierName }) => ({
                  value: supplierId,
                  label: supplierName,
                }));
              })
            } />
        }
        <ProFormText hidden={!props.disabled} disabled={props.disabled} name={fieldName} label=" " />
      </div>
    </div >
  )
}

export default ProFormObject;