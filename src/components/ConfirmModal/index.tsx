import { InfoCircleOutlined } from '@ant-design/icons';
import type { GetProps } from 'antd';
import { Modal } from 'antd';
import { useIntl } from 'umi';
export type ConfirmModalType = GetProps<typeof Modal> & { tips?: string; recordId?: React.Key };
export default (props: ConfirmModalType) => {
  const intl = useIntl();
  return (
    <Modal
      classNames={{
        header: 'flex flex-row justify-center',
        footer: 'flex flex-row-reverse justify-center gap-4',
      }}
      maskClosable={false}
      centered
      width={480}
      closable={false}
      okText={intl.formatMessage({ id: 'common.button.confirm' })}
      title={<InfoCircleOutlined className="text-[42px] text-[#FF7621]" />}
      {...props}
    >
      <span className="flex flex-row justify-center mt-2 mb-6">{props.tips}</span>
    </Modal>
  );
};
