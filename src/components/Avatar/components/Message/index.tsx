import ColumnRender from '@/components/ColumnRender';
import { queryEtcAfterDetailPagePost } from '@/pages/purchase/detail/services';
import type { ViewDetailModalProps } from '@/pages/system/message';
import MessageDetailModal from '@/pages/system/message/components/MessageDetailModal';
import {
  createReturnOrderByETC,
  getUnReadCount,
  queryMsgList,
  setRead,
} from '@/pages/system/message/services';
import { MsgBizType } from '@/pages/system/message/types/MsgBizType';
import { MsgJumpType } from '@/pages/system/message/types/MsgJumpType';
import { MsgStatus } from '@/pages/system/message/types/MsgStatus';
import type { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import { history } from '@@/core/history';
import { BellOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Badge, Button, Space, Spin, Tooltip } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { NoticeType } from '@/pages/system/messageMgr/types/noticeType';

const Message = () => {
  const { notification } = App.useApp();
  const getModalMessageTimer = useRef<any>();
  const messageIdRef = useRef<number>();
  const messageTypeRef = useRef<MsgBizType>();
  const [loading, setLoading] = useState(false);
  const [unReadCount, setUnReadCount] = useState(0);
  const [viewDetailModalProps, setViewDetailModalProps] = useState<ViewDetailModalProps>({
    visible: false,
  });
  /**
   * 轮询未读消息数目
   */
  const getUnReadCountFn = () => {
    getUnReadCount().then((result) => {
      setUnReadCount(result);
    });
  };

  useEffect(() => {
    getUnReadCountFn();
    const queryUnReadCountTimer = setInterval(() => {
      getUnReadCountFn();
    }, 3000);
    return () => {
      clearInterval(queryUnReadCountTimer);
    };
  }, []);

  /**
   * 轮询弹窗消息
   */
  const getModalMessage = () => {
    queryMsgList({
      noticeType: NoticeType.POPUP,
      isDelete: 0,
      msgStatus: MsgStatus.NoRead,
      pageNo: 1,
      pageSize: 1,
      overdueTime: dayjs().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss'),
    }).then((result) => {
      if (result?.data?.length) {
        messageIdRef.current = result?.data?.[0].id;
        messageTypeRef.current = result?.data?.[0].bizType;
        notification.open({
          message: <Content modalMessage={result?.data?.[0]} />,
          duration: 0,
          onClose: onClose,
        });
        clearInterval(getModalMessageTimer.current);
      }
    });
  };

  /**
   * 弹窗内容
   */
  const Content = (props: { modalMessage: MsgListItemEntity }) => {
    const { modalMessage } = props;
    return (
      <Spin spinning={loading}>
        <div className="mb-4">{modalMessage?.title}</div>
        <div
          className={classNames('mb-4', {
            'line-clamp-2': messageTypeRef.current === MsgBizType.System,
          })}
        >
          {ColumnRender.RichContentColumnRender(modalMessage?.content ?? '')}
        </div>
        <Space>
          {modalMessage.bizType == 0 && (
            <Button
              type="primary"
              onClick={() => {
                setViewDetailModalProps({
                  id: modalMessage.id,
                  visible: true,
                });
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                onClose();
              }}
            >
              查看详情
            </Button>
          )}
        </Space>
      </Spin>
    );
  };

  useEffect(() => {
    getModalMessage();
    getModalMessageTimer.current = setInterval(() => {
      getModalMessage();
    }, 3000);
    return () => {
      clearInterval(getModalMessageTimer.current);
    };
  }, []);

  // 关闭弹窗消息
  const onClose = () => {
    if (messageIdRef.current) {
      setRead([messageIdRef.current]);
    }
    notification.destroy();
    getModalMessageTimer.current = setInterval(() => {
      getModalMessage();
    }, 3000);
  };

  return (
    <>
      <Tooltip title="我的消息">
        <Badge count={unReadCount} dot={true} offset={[-8, 8]}>
          <BellOutlined
            className="p-2 rounded-lg  cursor-pointer hover:bg-[#00000008]"
            style={{ fontSize: 18 }}
            onClick={() => {
              history.push('/system/message');
            }}
          />
        </Badge>
      </Tooltip>
      <MessageDetailModal
        {...viewDetailModalProps}
        onClose={() => setViewDetailModalProps({ id: undefined, visible: false })}
      />
    </>
  );
};

export default Message;
