import type { Agg } from '@/components/GoodsSearch/components/GoodsList/types/Agg';
import { useIntl } from '@umijs/max';
import { Tabs } from 'antd';
import { useEffect, useState } from 'react';
import type { QueryGoodsMultiRequest } from './components/GoodsList/types/query.goods.multi.request';
import type { GoodsSearchBizType } from './types/BizType';
import { currentBizTabOptions, GoodsSearchTabType } from './types/TabType';

export interface GoodsSearchProps {
  /**
   * 业务场景
   */
  bizType: GoodsSearchBizType;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 门店Id
   */
  storeId?: string;
  /**
   * 添加商品事件
   * @param itemList
   */
  onAdd: (itemList: []) => void;
  /**
   * 已添加商品的集合, 门店端是itemSn，云端是etcNo
   */
  addedItemSns?: string[];
  /**
   * 仓库ID
   */
  warehouseId?: string;
  /**
   * 供应商ID
   */
  supplierId?: string;
  /**
   * 销售单号
   */
  saleOrderNo?: string;
}

const GoodsSearch = (props: GoodsSearchProps) => {
  const {
    bizType,
    cstId,
    storeId,
    onAdd,
    addedItemSns = [],
    warehouseId,
    supplierId,
    saleOrderNo,
  } = props;
  const intl = useIntl();
  console.log('addedItemSns', addedItemSns);

  // 当前选中的TAB
  const [activeTabKey, setActiveTabKey] = useState<GoodsSearchTabType>(
    GoodsSearchTabType.Synthesis,
  );
  // 最终查询列表的表单值
  const [formData, setFormData] = useState<QueryGoodsMultiRequest>();
  // VIN查询的过滤项数据（品牌/品类树）
  const [agg, setAgg] = useState<Agg>();

  useEffect(() => {
    setFormData({});
  }, [activeTabKey]);

  // 最终查询列表按钮事件
  const handleSearch = (formData: QueryGoodsMultiRequest) => {
    setFormData({
      ...formData,
      // @ts-ignore
      timestamp: Date.now(),
    });
  };

  // 组装Tab内容
  const tabItems = currentBizTabOptions({
    bizType,
    handleSearch,
    formData,
    cstId,
    storeId,
    onAdd,
    addedItemSns,
    activeTabKey,
    warehouseId,
    supplierId,
    saleOrderNo,
    setAgg,
    agg,
    intl,
  });

  return (
    <Tabs
      tabBarStyle={{ display: tabItems.length === 1 ? 'none' : 'block' }}
      items={tabItems}
      activeKey={activeTabKey}
      onChange={(val) => setActiveTabKey(val as GoodsSearchTabType)}
    />
  );
};

export default GoodsSearch;
