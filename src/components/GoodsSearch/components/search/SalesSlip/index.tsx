import {
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  QueryFilter,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Form, Space } from 'antd';
import {
  SalesSlipStatusEnum,
  SalesSlipStatusNameOptions,
} from '../../GoodsList/types/SalesSlipStatusEnum';
import type { QueryGoodsMultiRequest } from '../../GoodsList/types/query.goods.multi.request';
export interface SalesSlipProps {
  onSearch: (value: QueryGoodsMultiRequest) => void;
}

const SalesSlip = (props: SalesSlipProps) => {
  const { onSearch } = props;
  const intl = useIntl();
  return (
    <QueryFilter onFinish={onSearch} submitter={false} className="!p-0" defaultColsNumber={4}>
      <ProFormText
        label={intl.formatMessage({ id: 'goods.search.form.goodsInfo' })}
        name="itemInfo"
        placeholder={intl.formatMessage({ id: 'goods.search.form.goodsInfoPlaceholder' })}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'goods.search.form.orderStatus' })}
        name="orderStatusList"
        showSearch={true}
        debounceTime={300}
        mode={'multiple'}
        options={SalesSlipStatusNameOptions}
        initialValue={[SalesSlipStatusEnum.DRAFT]}
      />
      <ProFormText label={intl.formatMessage({ id: 'goods.search.form.orderNo' })} name="orderNo" />
      <ProFormDateRangePicker
        name="createTime"
        transform={(values) => {
          return {
            beginOrderTime: values ? values[0] : undefined,
            endOrderTime: values ? values[1] : undefined,
          };
        }}
        label={intl.formatMessage({ id: 'goods.search.form.orderTime' })}
      />
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {intl.formatMessage({ id: 'goods.search.button.query' })}
          </Button>
          <Button htmlType="reset">
            {intl.formatMessage({ id: 'goods.search.button.reset' })}
          </Button>
        </Space>
      </Form.Item>
    </QueryFilter>
  );
};

export default SalesSlip;
