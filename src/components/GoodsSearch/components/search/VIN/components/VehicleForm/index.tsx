import type { Agg } from '@/components/GoodsSearch/components/GoodsList/types/Agg';
import type { QueryGoodsMultiRequest } from '@/components/GoodsSearch/components/GoodsList/types/query.goods.multi.request';
import CarModelTitle from '@/components/GoodsSearch/components/search/VIN/components/CarModelTitle';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { flattenCategory, transformCategoryTree } from '@/utils/transformCategoryTree';
import { ProFormInstance, ProFormTreeSelect, QueryFilter } from '@ant-design/pro-components';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { RequestOptionsType } from '@ant-design/pro-utils/es/typing';
import { Button, Form, Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
import type { CarModelByVinCode } from '../../../../../types/CarModelByVinCode';
import VehicleList from '../VehicleList';

export interface VehicleFormProps {
  vehicleList?: CarModelByVinCode[];
  onSearch: (value: QueryGoodsMultiRequest) => void;
  bizType: GoodsSearchBizType;
  agg?: Agg;
}

const VehicleForm = (props: VehicleFormProps) => {
  const { vehicleList = [], onSearch, agg, bizType } = props;
  const formRef = useRef<ProFormInstance>();
  const [isVehicleListVisible, setIsVehicleListVisible] = useState(false);
  const [modelId, setModelId] = useState<string>();

  // 神策上报需要传递Name
  const brandList = useRef<RequestOptionsType[]>([]);
  const categoryList = useRef<RequestOptionsType[]>([]);

  useEffect(() => {
    if (vehicleList.length === 1) {
      setModelId(vehicleList[0].modelId);
    } else if (vehicleList.length > 1) {
      setIsVehicleListVisible(true);
    }
  }, [vehicleList]);

  useEffect(() => {
    if (modelId) {
      formRef.current?.resetFields(['brandIdList', 'categoryIdList']);
      formRef.current?.submit();
    }
  }, [modelId]);

  const handleSearch = (values: any) => {
    const data = { ...values, modelId };

    // 只给神策用
    if (data.brandIdList?.length) {
      data.brandIdAndNameList = brandList.current
        .filter((item) => data.brandIdList.includes(item.value))
        .map((item) => ({
          id: item.value,
          name: item.label,
        }));
    }

    // 只给神策用
    if (data.categoryIdList?.length) {
      data.categoryIdAndNameList = categoryList.current
        .filter((item) => data.categoryIdList.includes(item.value))
        .map((item) => ({
          id: item.value,
          name: item.label,
        }));
    }

    onSearch?.(data);
  };

  console.log('modelId', modelId);

  return (
    <>
      {modelId && (
        <div className="mb-4">
          <div className="mb-4 bg-[#f5f5f5] rounded mt-6 p-4">
            <div className="font-bold">解析结果</div>
            <div className="text-gray-600">
              <CarModelTitle item={vehicleList?.find((item) => item.modelId === modelId)} />
              {vehicleList && vehicleList.length > 1 && (
                <Button type={'link'} onClick={() => setIsVehicleListVisible(true)}>
                  重新选择
                </Button>
              )}
            </div>
          </div>
          <QueryFilter formRef={formRef} className="!p-0" submitter={false} onFinish={handleSearch}>
            <ProFormText
              name="queryKeyWord"
              label="商品信息"
              placeholder="请输商品名称/编码/OE/自编码"
            />
            <ProFormSelect
              // options={
              //   agg?.brandAgg?.map((item) => ({
              //     label: item.brandName,
              //     value: item.brandId,
              //   })) ?? []
              // }
              mode={'multiple'}
              label="品牌"
              name="brandIdList"
              showSearch={true}
              fieldProps={{
                filterOption: false,
                maxTagCount: 3,
                optionRender: (option) => <Space>{option.data.label}</Space>,
              }}
              request={async (query) => {
                const params: any = {
                  brandStatus: '1',
                  pageSize: 99,
                  pageNo: 1,
                  brandName: query.keyWords,
                };
                if (bizType === GoodsSearchBizType.PlatformPurchase) {
                  params.dataType = 0; // 平台采购查询标准商品
                }
                const result = await queryGoodsPropertyPage(params, 'brand').then((result) =>
                  result.data?.map((item) => ({
                    label: item.brandName,
                    dataType: item.dataType,
                    value: item.brandId,
                  })),
                );
                brandList.current = result;
                return result;
              }}
            />
            <ProFormTreeSelect
              // options={
              //   agg?.categoryAgg?.map((item) => ({
              //     label: item.categoryName,
              //     value: item.categoryId,
              //   })) ?? []
              // }
              label="商品分类"
              name="categoryIdList"
              fieldProps={{
                treeCheckable: true,
                maxTagCount: 3,
                filterTreeNode: (text, treeNode) => treeNode.text?.includes(text),
              }}
              request={async (query) => {
                const params: any = {
                  categoryStatus: 1,
                  pageSize: 999,
                  pageNo: 1,
                  isReturnTree: true,
                };
                if (bizType === GoodsSearchBizType.PlatformPurchase) {
                  params.dataType = 0; // 平台采购查询标准商品
                }
                const result = (await queryGoodsPropertyPage(params, 'category')) ?? [];
                categoryList.current = flattenCategory(result.data);
                return transformCategoryTree(result.data);
              }}
            />
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
                <Button htmlType="reset">重置</Button>
              </Space>
            </Form.Item>
          </QueryFilter>
        </div>
      )}
      <VehicleList
        vehicleList={vehicleList}
        modelId={modelId}
        setModelId={setModelId}
        visible={isVehicleListVisible}
        onClose={() => setIsVehicleListVisible(false)}
      />
    </>
  );
};

export default VehicleForm;
