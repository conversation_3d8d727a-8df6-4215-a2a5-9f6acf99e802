import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { flattenCategory, transformCategoryTree } from '@/utils/transformCategoryTree';
import {
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import { RequestOptionsType } from '@ant-design/pro-utils/es/typing';
import { useIntl } from '@umijs/max';
import { Button, Form, Space } from 'antd';
import { useRef } from 'react';
import type { QueryGoodsMultiRequest } from '../../GoodsList/types/query.goods.multi.request';
import { ProFormField } from '@ant-design/pro-form';

export interface SynthesisProps {
  onSearch: (value: QueryGoodsMultiRequest) => void;
  bizType: GoodsSearchBizType;
}

const Synthesis = (props: SynthesisProps) => {
  const { onSearch, bizType } = props;
  const intl = useIntl();
  // 神策上报需要传递Name
  const brandList = useRef<RequestOptionsType[]>([]);
  const categoryList = useRef<RequestOptionsType[]>([]);

  const handleSearch = (values: any) => {
    const data = { ...values };

    // 只给神策用
    if (data.brandIdList?.length) {
      data.brandIdAndNameList = brandList.current
        .filter((item) => data.brandIdList.includes(item.value))
        .map((item) => ({
          id: item.value,
          name: item.label,
        }));
    }

    // 只给神策用
    if (data.categoryIdList?.length) {
      data.categoryIdAndNameList = categoryList.current
        .filter((item) => data.categoryIdList.includes(item.value))
        .map((item) => ({
          id: item.value,
          name: item.label,
        }));
    }
    onSearch(data);
  };

  return (
    <QueryFilter onFinish={handleSearch} submitter={false} layout="vertical" className="!p-0 !pt-4">
      <ProFormText
        label={intl.formatMessage({ id: 'goods.search.form.goodsInfo' })}
        name="queryKeyWord"
        placeholder={intl.formatMessage({ id: 'goods.search.form.goodsInfoPlaceholder' })}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'goods.search.form.brand' })}
        name="brandIdList"
        showSearch={true}
        debounceTime={300}
        mode={'multiple'}
        fieldProps={{
          filterOption: false,
          maxTagCount: 3,
          optionRender: (option) => <Space>{option.data.label}</Space>,
        }}
        request={async (query) => {
          const params: any = {
            brandStatus: '1',
            pageSize: 99,
            pageNo: 1,
            brandName: query.keyWords,
          };
          if (bizType === GoodsSearchBizType.PlatformPurchase) {
            params.dataType = 0; // 平台采购查询标准商品
          }
          const result = await queryGoodsPropertyPage(params, 'brand').then((result) =>
            result.data?.map((item: any) => ({
              label: item.brandName,
              dataType: item.dataType,
              value: item.brandId,
            })),
          );
          brandList.current = result;
          return result;
        }}
        colProps={{
          span: 6,
        }}
      />
      <ProFormTreeSelect
        label={intl.formatMessage({ id: 'goods.search.form.category' })}
        name="categoryIdList"
        fieldProps={{
          maxTagCount: 3,
          treeCheckable: true,
          filterTreeNode: (text, treeNode) => treeNode.text?.includes(text),
        }}
        request={async () => {
          const params: any = {
            categoryStatus: 1,
            pageSize: 999,
            pageNo: 1,
            isReturnTree: true,
          };
          if (bizType === GoodsSearchBizType.PlatformPurchase) {
            params.dataType = 0; // 平台采购查询标准商品
          }
          const result = (await queryGoodsPropertyPage(params, 'category')) ?? [];
          categoryList.current = flattenCategory(result.data);
          return transformCategoryTree(result.data);
        }}
      />
      <ProFormField label=" ">
        <Space>
          <Button type="primary" htmlType="submit">
            {intl.formatMessage({ id: 'goods.search.button.query' })}
          </Button>
          <Button htmlType="reset">
            {intl.formatMessage({ id: 'goods.search.button.reset' })}
          </Button>
        </Space>
      </ProFormField>
    </QueryFilter>
  );
};

export default Synthesis;
