import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import {
  EmailTemplateEnum,
  EmailTemplateEnumName,
  EmailTemplateEnumPrefix,
} from '@/components/SendMail/types/email.template.enum';
import { sendMail } from '@/components/SendMail/services';
import { message } from 'antd';
import { useState } from 'react';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import Cookies from 'js-cookie';
import { useIntl } from 'umi';

export interface SendMailProps {
  // 模版类型
  templateType: EmailTemplateEnum;
  // 业务单号
  orderNo?: string;
  open?: boolean;
  onClose?: () => void;
}

const isDev = process.env.NODE_ENV === 'development';

export default function SendMail(props: SendMailProps) {
  const { templateType, orderNo, open, onClose } = props;
  const [loading, setLoading] = useState(false);
  const intl = useIntl();

  const handleSubmit = async (values: any) => {
    const data = { ...values, templateType, orderNo };
    // @ts-ignore
    data.prefix = EmailTemplateEnumPrefix[EmailTemplateEnum[templateType]];

    switch (templateType) {
      case EmailTemplateEnum.SALE_ORDER:
        data.redirectUrl = `${
          isDev ? 'https://pre-i0-gripxstoreweb.carzone360.com' : window.location.origin
        }/print?printType=${PrintType.salesOrder}&orderNo=${orderNo}&sid=${Cookies.get(
          's-session-id',
        )}`;
        data.landscape = true;
        break;
      case EmailTemplateEnum.SALE_RETURN_ORDER:
        break;
    }

    setLoading(true);
    sendMail(data)
      .then((res) => {
        if (res) {
          message.success(intl.formatMessage({ id: 'sales.order.list.sendSuccess' }));
          onClose?.();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <ModalForm
      open={open}
      modalProps={{
        onCancel: onClose,
        destroyOnClose: true,
      }}
      title={intl.formatMessage({ id: 'sales.order.list.sendEmail' })}
      width={500}
      onFinish={handleSubmit}
      loading={loading}
    >
      {/**@ts-ignore**/}
      <div className="my-4">{EmailTemplateEnumName[EmailTemplateEnum[templateType]]}</div>
      <ProFormSelect
        label={intl.formatMessage({ id: 'sales.order.list.receiveMan' })}
        required
        rules={[{ required: true }]}
        fieldProps={{
          mode: 'tags',
        }}
        name="to"
        placeholder={intl.formatMessage({ id: 'sales.order.list.sendPlaceholder' })}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'sales.order.list.carbonCopy' })}
        fieldProps={{
          mode: 'tags',
        }}
        name="cc"
        placeholder={intl.formatMessage({ id: 'sales.order.list.sendPlaceholder' })}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'sales.order.list.bcc' })}
        fieldProps={{
          mode: 'tags',
        }}
        name="bcc"
        placeholder={intl.formatMessage({ id: 'sales.order.list.sendPlaceholder' })}
      />
    </ModalForm>
  );
}
