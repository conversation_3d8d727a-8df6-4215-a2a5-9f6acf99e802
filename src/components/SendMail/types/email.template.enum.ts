export enum EmailTemplateEnum {
  /**
   * 报价单邮件
   */
  QUOTATION_ORDER = 'QUOTATION_ORDER',

  /**
   * 销售单邮件
   */
  SALE_ORDER = 'SALE_ORDER',

  /**
   * 销售退货单邮件
   */
  SALE_RETURN_ORDER = 'SALE_RETURN_ORDER',

  /**
   * 对账单邮件
   */
  STATEMENT_ORDER = 'STATEMENT_ORDER',

  /**
   * 付款邮件
   */
  PAYMENT = 'PAYMENT',

  /**
   * 收款确认邮件
   */
  PAYMENT_CONFIRMATION = 'PAYMENT_CONFIRMATION',

  /**
   * 商城下单成功邮件
   */
  EC_ORDER_SUCCESS = 'EC_ORDER_SUCCESS',
}

export enum EmailTemplateEnumName {
  QUOTATION_ORDER = ' 报价单邮件',
  SALE_ORDER = '销售单邮件',
  SALE_RETURN_ORDER = '销售退货单邮件',
  STATEMENT_ORDER = '对账单邮件',
  PAYMENT = '付款邮件',
  PAYMENT_CONFIRMATION = '收款确认邮件',
  EC_ORDER_SUCCESS = '商城下单成功邮件',
}

export enum EmailTemplateEnumPrefix {
  QUOTATION_ORDER = 'Sale',
  SALE_ORDER = 'Sale',
  SALE_RETURN_ORDER = 'Sale',
}
