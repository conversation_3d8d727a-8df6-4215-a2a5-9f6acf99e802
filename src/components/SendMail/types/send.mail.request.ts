import { EmailTemplateEnum } from '@/components/SendMail/types/email.template.enum';

export interface SendMailRequest {
  /**
   * 密送列表
   */
  bcc?: string[];
  /**
   * 抄送列表
   */
  cc?: string[];
  /**
   * None
   */
  extRemark?: string;
  /**
   * 根据printUrl生成的fileUrl
   */
  fileUr?: string;
  /**
   * None
   */
  firstName?: string;
  /**
   * false表示纵向，true表示横向，非必传
   */
  landscape?: boolean;
  /**
   * None
   */
  lastName?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 业务单号
   */
  orderNo?: string;
  /**
   * pdf前缀
   */
  prefix?: string;
  /**
   * 打印地址，有则生成pdf，以附件形式发送邮箱
   */
  printUrl?: string;
  /**
   * 模版名称
   */
  templateType?: EmailTemplateEnum;
  /**
   * 发送列表
   */
  to?: string[];
}
