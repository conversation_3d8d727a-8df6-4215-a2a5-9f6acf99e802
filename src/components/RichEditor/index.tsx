import BraftEditor, { type EditorState } from 'braft-editor';
import 'braft-editor/dist/index.css';
import { uploadFile } from '@/services/systerm';
import { FormInstance, GetProps } from 'antd';
import { DrawerForm } from '@ant-design/pro-form';

export interface RichEditorProps {
  readOnly?: boolean;
  form?: FormInstance<any>;
  setContent?: (v: EditorState) => void;
  content?: EditorState;
  name?: string;
}

export default function RichEditor(props: RichEditorProps) {
  const buildPreviewHtml = () => {
    const html = `
      <!Doctype html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
          <meta http-equiv="X-UA-Compatible" content="ie=edge">
          <title>预览</title>
          <style>
            html,body{
              margin: 0;
              padding: 0;
            }
            img{
              width:100%;
            }
          </style>
        </head>
        <body>
          <div class="container">${props.content ? props.content.toHTML() : ''}</div>
        </body>
      </html>
    `;
    return html;
  };

  const preview = () => {
    const currentWindow = window as any;
    if (currentWindow.previewWindow) {
      currentWindow.previewWindow.close();
    }
    currentWindow.previewWindow = window.open();
    currentWindow.previewWindow.document.write(buildPreviewHtml());
    currentWindow.previewWindow.document.close();
  };

  const handleImageUpload = async (params: any) => {
    console.log(params);
    const { id = '', file, success } = params;
    const formData = new FormData();
    formData.append('file', file);
    formData.append('expireTime', '315360000');
    const result = await uploadFile(formData);
    if (result) {
      success({
        // @ts-ignore
        url: result[0],
        meta: {
          id: id,
          title: file.name,
          alt: file.name,
          loop: false,
          autoPlay: false,
          controls: false,
          poster: file.name,
        },
      });
    }
  };

  const editorProps: GetProps<typeof BraftEditor> = {};
  const submitterProps: GetProps<typeof DrawerForm> = {};
  if (props.readOnly) {
    editorProps.controls = [];
    editorProps.extendControls = [];
    editorProps.readOnly = props.readOnly;
    submitterProps.submitter = false;
  }
  return (
    <BraftEditor
      className="border border-solid border-[#d9d9d9]"
      value={props.content}
      media={{
        uploadFn: handleImageUpload,
        accepts: {
          image: 'image/png,image/jpeg,image/gif,image/jpg',
          video: false,
          audio: false,
        },
        externals: {
          image: false,
          video: false,
          audio: false,
        },
      }}
      extendControls={[
        {
          key: 'custom-button',
          type: 'button',
          text: '预览',
          title: '预览',
          className: 'pointer-events-auto',
          onClick: preview,
        },
      ]}
      excludeControls={['emoji']}
      onChange={(editorState: EditorState) => {
        props.form?.setFieldValue(props.name, editorState);
        props.setContent?.(editorState);
      }}
      {...editorProps}
    />
  );
}
