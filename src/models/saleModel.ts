import { ConfigType } from '@/pages/system/config/components/types/ConfigType';
import { querySysPropertyList, setSysProperty } from '@/services/systerm';
import { ColumnsState } from '@ant-design/pro-components';
import { useState } from 'react';

export const columnGoodsToSale: Record<string, string> = {
  oeNos: 'oeNo',
  itemName: 'itemName',
  brandPartNos: 'brandPartNo',
  brandName: 'brandName',
  categoryName: 'categoryName',
  remark: 'remark',
  avaNum: 'avaNum',
  inventoryNum: 'inventoryNum',
  suggestPrice: 'origPriceYuan',
  lastSalePrice: 'lastSalePrice',
  price: 'unitPriceYuan',
  number: 'saleNum',
};

export default function saleModel() {
  const [columnsStateValue, setColumnsStateValue] = useState<Record<string, ColumnsState>>({});

  console.log('columnsStateValue', columnsStateValue);

  const saleColumnsStateValue = (() => {
    return Object.keys(columnsStateValue).reduce((acc, key) => {
      return { ...acc, [columnGoodsToSale[key] ?? key]: columnsStateValue[key] };
    }, {});
  })();

  console.log('saleColumnsStateValue', saleColumnsStateValue);

  const querySaleColumns = () => {
    querySysPropertyList({
      type: ConfigType.SaleOrderStoreColumn,
      propDimensions: 'ACCOUNT',
    }).then((result) => {
      const columnValue =
        result?.filter((result) => result.type === ConfigType.SaleOrderStoreColumn)[0]?.value ||
        '{}';

      try {
        setColumnsStateValue(JSON.parse(columnValue));
      } catch (error) {
        console.log(error);
      }
    });
  };

  const onColumnsStatChange = (data) => {
    setColumnsStateValue(data);
    setSysProperty({
      propDimensions: 'ACCOUNT',
      type: ConfigType.SaleOrderStoreColumn,
      value: data,
    });
  };

  return { columnsStateValue, saleColumnsStateValue, onColumnsStatChange, querySaleColumns };
}
